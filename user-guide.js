/**
 * 用户引导系统 - 基于Driver.js的新手引导功能
 * 
 * 功能特性：
 * - 多步骤引导流程
 * - 持久化存储用户状态
 * - 响应式设计适配
 * - 可访问性支持
 * - 主题系统集成
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

class UserGuideManager {
    constructor(options = {}) {
        this.options = {
            // 默认配置
            storageKey: 'user_guide_completed',
            versionKey: 'user_guide_version',
            currentVersion: '1.0.0',
            autoStart: true,
            showProgress: true,
            allowClose: true,
            overlayOpacity: 0.75,
            smoothScroll: true,
            ...options
        };

        this.driver = null;
        this.isInitialized = false;
        this.currentStep = 0;
        this.interactiveStepActive = false;
        this.originalMenuClickHandlers = new Map();
        this.currentInteractiveHandler = null;

        // 绑定方法上下文
        this.init = this.init.bind(this);
        this.start = this.start.bind(this);
        this.restart = this.restart.bind(this);
        this.skip = this.skip.bind(this);
        this.reset = this.reset.bind(this);
        this.handleInteractiveStep = this.handleInteractiveStep.bind(this);
    }

    /**
     * 初始化引导系统
     */
    async init() {
        try {
            // 检查Driver.js是否已加载
            const DriverConstructor = window.driver?.driver || window.Driver;
            if (typeof DriverConstructor === 'undefined') {
                console.warn('Driver.js 未加载，正在动态加载...');
                await this.loadDriverJS();
            }

            // 初始化Driver实例
            const FinalDriverConstructor = window.driver?.driver || window.Driver;
            this.driver = FinalDriverConstructor(this.getDriverConfig());
            this.isInitialized = true;

            console.log('✅ 用户引导系统初始化成功');

            // 检查是否需要自动开始引导
            if (this.options.autoStart && this.shouldShowGuide()) {
                // 延迟启动，确保页面完全加载
                setTimeout(() => this.start(), 1000);
            }

            return true;
        } catch (error) {
            console.error('❌ 用户引导系统初始化失败:', error);
            return false;
        }
    }

    /**
     * 动态加载Driver.js库
     */
    loadDriverJS() {
        return new Promise((resolve, reject) => {
            const DriverConstructor = window.driver?.driver || window.Driver;
            if (typeof DriverConstructor !== 'undefined') {
                resolve();
                return;
            }

            // 监听Driver.js加载完成事件
            const handleDriverLoaded = () => {
                window.removeEventListener('driverLoaded', handleDriverLoaded);
                resolve();
            };
            window.addEventListener('driverLoaded', handleDriverLoaded);

            // 动态加载Driver.js脚本
            const script = document.createElement('script');
            script.src = 'libs/driver.js/driver.min.js';
            script.onerror = reject;
            document.head.appendChild(script);

            // 同时加载CSS
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = 'libs/driver.js/driver.min.css';
            document.head.appendChild(link);
        });
    }

    /**
     * 获取Driver.js配置
     */
    getDriverConfig() {
        return {
            className: 'user-guide-driver-enhanced',
            animate: true,
            opacity: 0.85, // 优化遮罩透明度，提升可见性
            padding: 20, // 增加内边距，提升可读性
            allowClose: this.options.allowClose,
            overlayClickNext: false,
            overlayOpacity: 0.85, // 明确设置遮罩透明度
            popoverClass: 'driver-popover user-guide-popover', // 添加自定义类
            doneBtnText: '完成引导',
            closeBtnText: '跳过',
            nextBtnText: '下一步',
            prevBtnText: '上一步',
            showButtons: ['next', 'previous', 'close'],
            keyboardControl: true, // 支持键盘导航（无障碍访问）
            smoothScroll: this.options.smoothScroll,
            showProgress: true,
            progressText: '第 {{current}} 步，共 {{total}} 步',

            // 无障碍访问支持
            ariaLabelledBy: 'driver-popover-title',
            ariaDescribedBy: 'driver-popover-description',

            // 动画配置
            animationDuration: 300,

            // 高亮配置
            stagePadding: 8,
            stageRadius: 8,

            // 事件回调
            onHighlightStarted: (element) => {
                this.onStepStart(element);
                // 确保元素可见性
                this.ensureElementVisibility(element);
            },
            onHighlighted: (element) => {
                this.onStepHighlighted(element);
                // 添加无障碍访问属性
                this.addAccessibilityAttributes(element);
                // 温和的定位检查
                this.forceFixPositioning();
            },
            onDeselected: (element) => {
                this.onStepEnd(element);
                // 清理无障碍访问属性
                this.removeAccessibilityAttributes(element);
            },
            onReset: () => {
                this.onGuideComplete();
            },
            onDestroyed: () => {
                // 清理所有高亮和样式
                this.cleanup();
            }
        };
    }

    /**
     * 获取引导步骤配置
     */
    getGuideSteps() {
        return [
            {
                element: 'body',
                popover: {
                    className: 'guide-step-welcome',
                    title: '👋 欢迎使用跨境运营助手！',
                    description: '我将为您介绍平台的主要功能，帮助您快速上手。整个引导包含 <strong>9个步骤</strong>，其中包括交互式体验。<br><br><strong>💡 提示：</strong>您可以随时按 ESC 键或点击"跳过"来结束引导。',
                    position: 'center',
                    showButtons: ['next', 'close']
                }
            },
            {
                element: '.sidebar',
                popover: {
                    className: 'guide-step-sidebar',
                    title: '📋 主导航菜单',
                    description: '这里是主要的功能导航区域。接下来我们将通过<strong>交互式体验</strong>来了解各个功能模块：<br>• <strong>仪表盘</strong> - 数据概览<br>• <strong>产品库</strong> - 商品管理<br>• <strong>建联记录</strong> - 合作跟踪<br>• <strong>AI助手</strong> - 智能分析',
                    position: 'right'
                }
            },
            {
                element: '.menu-item:first-child',
                popover: {
                    className: 'guide-step-interactive',
                    title: '🎯 交互体验：仪表盘',
                    description: '现在请<strong>点击"仪表盘"菜单项</strong>来查看数据概览功能。<br><br>仪表盘将展示：<br>• 📊 关键业务数据<br>• 📈 合作进展统计<br>• 🔔 最新活动动态<br><br>💡 <strong>提示：</strong>点击下方的"开始交互"按钮，然后点击仪表盘菜单项即可继续。',
                    position: 'right',
                    showButtons: ['previous', 'next'],
                    nextBtnText: '开始交互',
                    onNext: () => this.handleInteractiveStep('dashboard')
                }
            },
            {
                element: '.menu-item:nth-child(2)',
                popover: {
                    className: 'guide-step-interactive',
                    title: '🎯 交互体验：产品库',
                    description: '请<strong>点击"产品库"菜单项</strong>来了解商品管理功能。<br><br>产品库功能包括：<br>• 📦 商品信息管理<br>• 🏷️ 标签分类系统<br>• 🔍 智能搜索筛选<br><br>💡 <strong>提示：</strong>点击下方的"开始交互"按钮，然后点击产品库菜单项即可继续。',
                    position: 'right',
                    showButtons: ['previous', 'next'],
                    nextBtnText: '开始交互',
                    onNext: () => this.handleInteractiveStep('products')
                }
            },
            {
                element: '.menu-item:nth-child(3)',
                popover: {
                    className: 'guide-step-interactive',
                    title: '🎯 交互体验：建联记录',
                    description: '请<strong>点击"建联记录"菜单项</strong>来查看合作跟踪功能。<br><br>建联记录帮您：<br>• 👥 管理博主联系信息<br>• 📧 跟踪邮件往来<br>• 📊 监控合作进展<br><br>💡 <strong>提示：</strong>点击下方的"开始交互"按钮，然后点击建联记录菜单项即可继续。',
                    position: 'right',
                    showButtons: ['previous', 'next'],
                    nextBtnText: '开始交互',
                    onNext: () => this.handleInteractiveStep('outreach')
                }
            },
            {
                element: '#ai-assistant-menu',
                popover: {
                    className: 'guide-step-ai',
                    title: '🤖 AI助手 - 核心功能',
                    description: 'AI助手是平台的<strong>核心功能</strong>，可以帮您：<br>• 🔍 分析商品信息和特性<br>• 🎯 推荐合适的YouTube博主<br>• ✉️ 生成专业的合作邮件<br>• 📈 评估合作伙伴匹配度',
                    position: 'right'
                }
            },
            {
                element: '.central-input-container',
                popover: {
                    className: 'guide-step-input',
                    title: '✨ 开始分析',
                    description: '在这个输入框中<strong>粘贴商品链接</strong>或描述，AI将自动分析并推荐最适合的合作博主。<br><br>🎯 <strong>快速体验：</strong>点击"试用演示"按钮可以立即体验功能！',
                    position: 'top'
                }
            },
            {
                element: '.notification-container',
                popover: {
                    className: 'guide-step-notifications',
                    title: '🔔 通知中心',
                    description: '这里会显示重要通知：<br>• 📬 博主回复消息<br>• 📈 合作进展更新<br>• 🎥 视频发布通知<br>• ⚠️ 系统重要提醒<br><br>💡 保持关注，不错过任何重要信息！',
                    position: 'bottom-left'
                }
            },
            {
                element: '#user-profile-sidebar',
                popover: {
                    className: 'guide-step-profile',
                    title: '👤 个人中心',
                    description: '点击这里可以：<br>• ⚙️ 管理账户设置<br>• 📧 配置邮箱信息<br>• 👤 查看个人资料<br>• 🔄 <strong>重新启动引导</strong>（在"新手引导"选项中）',
                    position: 'right'
                }
            },
            {
                element: 'body',
                popover: {
                    className: 'guide-step-complete',
                    title: '🎉 引导完成！',
                    description: '恭喜您已经了解了平台的主要功能！通过交互式体验，您现在对各个模块有了更深入的了解。<br><br>🚀 <strong>下一步：</strong>开始使用AI助手分析您的商品吧！<br><br>📚 <strong>需要帮助？</strong>在用户菜单中找到"新手引导"选项可以重新查看。',
                    position: 'center',
                    showButtons: ['close']
                }
            }
        ];
    }

    /**
     * 获取高级引导步骤（针对特定功能的详细引导）
     */
    getAdvancedGuideSteps(feature) {
        const advancedSteps = {
            'ai-assistant': [
                {
                    element: '.central-input-container',
                    popover: {
                        title: '🎯 AI助手详细使用',
                        description: '让我详细介绍AI助手的使用方法...',
                        position: 'top'
                    }
                }
                // 可以添加更多特定功能的引导步骤
            ],
            'product-analysis': [
                {
                    element: '.quick-prompt-btn[data-prompt="demo"]',
                    popover: {
                        title: '🔍 商品分析演示',
                        description: '点击这里开始商品分析演示...',
                        position: 'top'
                    }
                }
            ]
        };

        return advancedSteps[feature] || [];
    }

    /**
     * 检查元素是否存在并可见
     */
    isElementVisible(selector) {
        try {
            const element = document.querySelector(selector);
            if (!element) return false;

            const rect = element.getBoundingClientRect();
            const style = window.getComputedStyle(element);

            return rect.width > 0 &&
                   rect.height > 0 &&
                   style.display !== 'none' &&
                   style.visibility !== 'hidden' &&
                   style.opacity !== '0';
        } catch (error) {
            console.warn('检查元素可见性时出错:', selector, error);
            return false;
        }
    }

    /**
     * 等待元素出现
     */
    waitForElement(selector, timeout = 5000) {
        return new Promise((resolve, reject) => {
            if (this.isElementVisible(selector)) {
                resolve(document.querySelector(selector));
                return;
            }

            const observer = new MutationObserver((_, obs) => {
                if (this.isElementVisible(selector)) {
                    obs.disconnect();
                    resolve(document.querySelector(selector));
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['style', 'class']
            });

            setTimeout(() => {
                observer.disconnect();
                reject(new Error(`元素 ${selector} 在 ${timeout}ms 内未出现`));
            }, timeout);
        });
    }

    /**
     * 开始引导
     */
    start() {
        if (!this.isInitialized) {
            console.warn('引导系统未初始化，正在初始化...');
            this.init().then(() => {
                if (this.isInitialized) {
                    this.start();
                }
            });
            return;
        }

        try {
            const steps = this.getGuideSteps();

            // 重新创建driver实例并传入步骤
            const DriverConstructor = window.driver?.driver || window.Driver;
            const config = this.getDriverConfig();
            config.steps = steps; // 直接在配置中传入步骤

            this.driver = DriverConstructor(config);
            this.driver.drive(); // 使用drive()方法启动

            // 🎯 启动位置监控
            this.startPositionMonitoring();

            // 触发开始事件
            this.triggerEvent('onStart');

            console.log('🚀 用户引导已开始');
        } catch (error) {
            console.error('❌ 启动引导失败:', error);
            console.error('错误详情:', error.stack);
        }
    }

    /**
     * 重新开始引导
     */
    restart() {
        this.reset();
        this.start();
    }

    /**
     * 跳过引导
     */
    skip() {
        if (this.driver) {
            this.driver.destroy(); // 使用destroy()方法替代reset()
        }
        this.stopPositionMonitoring(); // 停止位置监控
        this.markAsCompleted();
        this.triggerEvent('onSkip');
        console.log('⏭️ 用户跳过了引导');
    }

    /**
     * 重置引导状态
     */
    reset() {
        localStorage.removeItem(this.options.storageKey);
        localStorage.removeItem(this.options.versionKey);
        console.log('🔄 引导状态已重置');
    }

    /**
     * 检查是否应该显示引导
     */
    shouldShowGuide() {
        // 检查是否已完成引导
        const completed = localStorage.getItem(this.options.storageKey);
        const version = localStorage.getItem(this.options.versionKey);
        
        // 如果未完成或版本不匹配，则显示引导
        return !completed || version !== this.options.currentVersion;
    }

    /**
     * 标记引导为已完成
     */
    markAsCompleted() {
        localStorage.setItem(this.options.storageKey, 'true');
        localStorage.setItem(this.options.versionKey, this.options.currentVersion);
    }

    /**
     * 检查引导是否已完成
     */
    isCompleted() {
        const completed = localStorage.getItem(this.options.storageKey);
        const version = localStorage.getItem(this.options.versionKey);
        return completed === 'true' && version === this.options.currentVersion;
    }

    // 事件处理方法
    onStepStart(element) {
        this.currentStep++;
        this.triggerEvent('onStepStart', { element, step: this.currentStep });
    }

    onStepHighlighted(element) {
        // 确保高亮元素可见
        this.ensureElementVisibility(element);
        this.triggerEvent('onStepHighlighted', { element, step: this.currentStep });
    }

    /**
     * 确保元素可见性
     */
    ensureElementVisibility(element) {
        if (element && element.scrollIntoView) {
            element.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
                inline: 'nearest'
            });
        }
    }

    /**
     * 智能定位修复 - 确保弹出框在正确位置 (强力版)
     */
    fixPopoverPosition() {
        const popovers = document.querySelectorAll('.driver-popover');
        popovers.forEach(popover => {
            if (!popover) return;

            // 打印当前样式，看看是什么在捣乱
            console.log('POPOVER DEBUG: Current inline style before fix:', popover.getAttribute('style'));

            // 无条件强制居中
            console.log('🔧 强制修复弹出框位置...');
            Object.assign(popover.style, {
                position: 'fixed',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                margin: '0',
                zIndex: '2147483647', // 再次确保最高z-index
                display: 'block' // 强制显示
            });
            console.log('POPOVER DEBUG: Style after fix:', popover.getAttribute('style'));
        });
    }

    /**
     * 持续监控定位 - 防止弹出框跑偏 (更频繁)
     */
    startPositionMonitoring() {
        // 初始修复
        setTimeout(() => this.fixPopoverPosition(), 50); // 提前首次修复
        
        // 定期检查
        this.positionInterval = setInterval(() => {
            this.fixPopoverPosition();
        }, 200); // 缩短检查间隔
    }

    /**
     * 停止定位监控
     */
    stopPositionMonitoring() {
        if (this.positionInterval) {
            clearInterval(this.positionInterval);
            this.positionInterval = null;
        }
    }

    /**
     * 温和的定位优化 - (此方法在此次修复中不再使用，但保留以备后用)
     */
    forceFixPositioning() {
       // ...
    }

    /**
     * 添加无障碍访问属性
     */
    addAccessibilityAttributes(element) {
        if (element) {
            element.setAttribute('aria-describedby', 'driver-popover-description');
            element.setAttribute('role', 'button');
            element.setAttribute('tabindex', '0');
        }
    }

    /**
     * 移除无障碍访问属性
     */
    removeAccessibilityAttributes(element) {
        if (element) {
            element.removeAttribute('aria-describedby');
            element.removeAttribute('role');
            element.removeAttribute('tabindex');
        }
    }

    /**
     * 清理所有样式和属性
     */
    cleanup() {
        // 移除所有高亮样式
        document.querySelectorAll('.guide-menu-item-highlight').forEach(el => {
            el.classList.remove('guide-menu-item-highlight');
        });

        // 移除所有无障碍访问属性
        document.querySelectorAll('[aria-describedby="driver-popover-description"]').forEach(el => {
            this.removeAccessibilityAttributes(el);
        });

        console.log('🧹 引导系统清理完成');
    }

    onStepEnd(element) {
        this.triggerEvent('onStepEnd', { element, step: this.currentStep });
    }

    onGuideComplete() {
        this.markAsCompleted();
        this.currentStep = 0;
        this.stopPositionMonitoring(); // 停止位置监控
        this.triggerEvent('onComplete');
        console.log('✅ 用户引导已完成');
    }

    /**
     * 触发自定义事件
     */
    triggerEvent(eventName, data = {}) {
        if (typeof this.options[eventName] === 'function') {
            this.options[eventName](data);
        }
        
        // 触发DOM事件
        const event = new CustomEvent(`userguide:${eventName.toLowerCase()}`, {
            detail: data
        });
        document.dispatchEvent(event);
    }

    /**
     * 启动特定功能的引导
     */
    startFeatureGuide(feature) {
        if (!this.isInitialized) {
            console.warn('引导系统未初始化');
            return;
        }

        const steps = this.getAdvancedGuideSteps(feature);
        if (steps.length === 0) {
            console.warn(`未找到功能 ${feature} 的引导步骤`);
            return;
        }

        try {
            // 重新创建driver实例并传入步骤
            const DriverConstructor = window.driver?.driver || window.Driver;
            const config = this.getDriverConfig();
            config.steps = steps; // 直接在配置中传入步骤

            this.driver = DriverConstructor(config);
            this.driver.drive(); // 使用drive()方法启动
            console.log(`🎯 ${feature} 功能引导已开始`);
        } catch (error) {
            console.error(`❌ 启动 ${feature} 功能引导失败:`, error);
        }
    }

    /**
     * 获取引导统计信息
     */
    getStats() {
        return {
            isCompleted: this.isCompleted(),
            version: localStorage.getItem(this.options.versionKey),
            completedAt: localStorage.getItem(this.options.storageKey + '_completed_at'),
            totalSteps: this.getGuideSteps().length,
            currentStep: this.currentStep
        };
    }

    /**
     * 设置引导完成时间
     */
    markAsCompleted() {
        localStorage.setItem(this.options.storageKey, 'true');
        localStorage.setItem(this.options.versionKey, this.options.currentVersion);
        localStorage.setItem(this.options.storageKey + '_completed_at', new Date().toISOString());
    }

    /**
     * 检查浏览器兼容性
     */
    checkCompatibility() {
        const features = {
            localStorage: typeof Storage !== 'undefined',
            customEvents: typeof CustomEvent !== 'undefined',
            mutationObserver: typeof MutationObserver !== 'undefined',
            getBoundingClientRect: typeof Element.prototype.getBoundingClientRect === 'function'
        };

        const isCompatible = Object.values(features).every(Boolean);

        if (!isCompatible) {
            console.warn('浏览器兼容性检查失败:', features);
        }

        return { isCompatible, features };
    }

    /**
     * 处理交互式步骤
     */
    handleInteractiveStep(stepType) {
        this.interactiveStepActive = true;

        // 关闭当前的driver引导
        if (this.driver) {
            this.driver.destroy();
        }

        // 高亮对应的菜单项
        this.highlightMenuItem(stepType);

        // 显示交互提示
        this.showInteractivePrompt(stepType);

        // 设置菜单项点击监听器
        this.setupInteractiveMenuListener(stepType);

        console.log(`🎯 交互式步骤激活: ${stepType}`);
    }

    /**
     * 显示交互提示
     */
    showInteractivePrompt(stepType) {
        // 创建临时提示框
        const promptElement = document.createElement('div');
        promptElement.className = 'interactive-guide-prompt';
        promptElement.innerHTML = `
            <div class="prompt-content">
                <div class="prompt-icon">👆</div>
                <div class="prompt-text">请点击高亮的菜单项继续引导</div>
                <div class="prompt-skip">
                    <button class="skip-interactive-btn" onclick="userGuide.continueGuide()">跳过交互</button>
                </div>
            </div>
        `;

        // 添加样式
        promptElement.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border: 2px solid var(--primary-color, #2196f3);
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
            z-index: 1000001;
            text-align: center;
            min-width: 300px;
            backdrop-filter: blur(10px);
        `;

        document.body.appendChild(promptElement);

        // 3秒后自动移除提示
        setTimeout(() => {
            if (promptElement.parentNode) {
                promptElement.remove();
            }
        }, 3000);
    }

    /**
     * 高亮菜单项
     */
    highlightMenuItem(stepType) {
        // 移除之前的高亮
        document.querySelectorAll('.guide-menu-item-highlight').forEach(el => {
            el.classList.remove('guide-menu-item-highlight');
        });

        let selector;
        switch(stepType) {
            case 'dashboard':
                selector = '.menu-item:first-child';
                break;
            case 'products':
                selector = '.menu-item:nth-child(2)';
                break;
            case 'outreach':
                selector = '.menu-item:nth-child(3)';
                break;
        }

        if (selector) {
            const menuItem = document.querySelector(selector);
            if (menuItem) {
                menuItem.classList.add('guide-menu-item-highlight');
                
                // 添加动画效果
                menuItem.style.animation = 'guidePulse 2s infinite';
            }
        }
    }

    /**
     * 设置交互式菜单监听器
     */
    setupInteractiveMenuListener(stepType) {
        let selector;
        switch(stepType) {
            case 'dashboard':
                selector = '.menu-item:first-child .menu-item-content';
                break;
            case 'products':
                selector = '.menu-item:nth-child(2) .menu-item-content';
                break;
            case 'outreach':
                selector = '.menu-item:nth-child(3) .menu-item-content';
                break;
        }

        if (selector) {
            const menuElement = document.querySelector(selector);
            if (menuElement) {
                // 保存原始点击处理器
                const originalHandler = menuElement.onclick;
                this.originalMenuClickHandlers.set(stepType, originalHandler);

                // 创建一个持久的点击处理器
                const persistentHandler = (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.handleMenuItemClick(stepType, originalHandler);
                };

                // 移除现有的监听器（如果有）
                if (this.currentInteractiveHandler) {
                    menuElement.removeEventListener('click', this.currentInteractiveHandler);
                }

                // 添加新的监听器
                this.currentInteractiveHandler = persistentHandler;
                menuElement.addEventListener('click', persistentHandler);

                // 5秒后自动继续（防止用户卡住）
                setTimeout(() => {
                    if (this.interactiveStepActive) {
                        console.log('⏰ 交互超时，自动继续引导');
                        this.handleMenuItemClick(stepType, originalHandler);
                    }
                }, 5000);
            }
        }
    }

    /**
     * 处理菜单项点击
     */
    handleMenuItemClick(stepType, originalHandler) {
        // 移除交互提示
        const promptElement = document.querySelector('.interactive-guide-prompt');
        if (promptElement) {
            promptElement.remove();
        }

        // 移除高亮和动画
        document.querySelectorAll('.guide-menu-item-highlight').forEach(el => {
            el.classList.remove('guide-menu-item-highlight');
            el.style.animation = '';
        });

        // 移除当前的交互监听器
        if (this.currentInteractiveHandler) {
            const menuElement = document.querySelector(this.getMenuSelector(stepType) + ' .menu-item-content');
            if (menuElement) {
                menuElement.removeEventListener('click', this.currentInteractiveHandler);
            }
            this.currentInteractiveHandler = null;
        }

        // 执行原始点击处理器（切换页面）
        if (originalHandler) {
            originalHandler();
        } else {
            // 手动触发页面切换
            this.switchToPage(stepType);
        }

        // 显示成功提示
        this.showSuccessMessage(stepType);

        // 延迟显示页面介绍
        setTimeout(() => {
            this.showPageIntroduction(stepType);
        }, 800);

        this.interactiveStepActive = false;
    }

    /**
     * 显示成功提示
     */
    showSuccessMessage(stepType) {
        const successElement = document.createElement('div');
        successElement.className = 'interaction-success-message';
        successElement.innerHTML = `
            <div class="success-content">
                <div class="success-icon">✅</div>
                <div class="success-text">太棒了！继续探索功能...</div>
            </div>
        `;

        successElement.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4caf50;
            color: white;
            border-radius: 8px;
            padding: 12px 20px;
            box-shadow: 0 4px 16px rgba(76,175,80,0.3);
            z-index: 1000002;
            animation: slideInRight 0.3s ease;
        `;

        document.body.appendChild(successElement);

        // 2秒后移除
        setTimeout(() => {
            successElement.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (successElement.parentNode) {
                    successElement.remove();
                }
            }, 300);
        }, 2000);
    }

    /**
     * 切换到指定页面
     */
    switchToPage(stepType) {
        // 隐藏所有容器
        document.querySelectorAll('.container').forEach(container => {
            container.style.display = 'none';
        });

        // 显示对应容器
        let containerId;
        switch(stepType) {
            case 'dashboard':
                containerId = 'dashboard-container';
                break;
            case 'products':
                containerId = 'product-container';
                break;
            case 'outreach':
                containerId = 'outreach-container';
                break;
        }

        if (containerId) {
            const container = document.getElementById(containerId);
            if (container) {
                container.style.display = 'block';
            }
        }

        // 更新菜单激活状态
        document.querySelectorAll('.menu-item').forEach(item => {
            item.classList.remove('active');
        });

        const activeMenuItem = document.querySelector(this.getMenuSelector(stepType));
        if (activeMenuItem) {
            activeMenuItem.classList.add('active');
        }
    }

    /**
     * 获取菜单选择器
     */
    getMenuSelector(stepType) {
        switch(stepType) {
            case 'dashboard':
                return '.menu-item:first-child';
            case 'products':
                return '.menu-item:nth-child(2)';
            case 'outreach':
                return '.menu-item:nth-child(3)';
            default:
                return '';
        }
    }

    /**
     * 显示页面介绍
     */
    showPageIntroduction(stepType) {
        let title, description, targetElement;

        switch(stepType) {
            case 'dashboard':
                title = '📊 仪表盘 - 数据概览';
                description = '这里展示了您的关键业务数据：<br>• 📈 合作项目统计<br>• 💰 收益分析<br>• 🎯 目标完成情况<br>• 📅 最新活动动态<br><br>通过数据驱动决策，优化您的合作策略！';
                targetElement = '#dashboard-container';
                break;
            case 'products':
                title = '📦 产品库 - 商品管理';
                description = '在这里管理您的所有商品：<br>• 🏷️ 添加和编辑商品信息<br>• 🔍 智能搜索和筛选<br>• 📊 查看商品分析结果<br>• 🎯 匹配合适的博主<br><br>高效管理商品，提升合作效率！';
                targetElement = '#product-container';
                break;
            case 'outreach':
                title = '👥 建联记录 - 合作跟踪';
                description = '跟踪所有合作进展：<br>• 📧 邮件往来记录<br>• 📊 合作状态监控<br>• 👤 博主信息管理<br>• 📈 合作效果分析<br><br>系统化管理，不错过任何合作机会！';
                targetElement = '#outreach-container';
                break;
        }

        if (title && description && targetElement) {
            // 创建临时引导实例来显示页面介绍
            const DriverConstructor = window.driver?.driver || window.Driver;
            const tempDriver = DriverConstructor({
                className: 'user-guide-driver-enhanced',
                animate: true,
                opacity: 0.8,
                padding: 12,
                allowClose: false,
                overlayClickNext: false,
                doneBtnText: '继续引导',
                nextBtnText: '继续引导',
                showButtons: ['next'],
                steps: [{
                    element: targetElement,
                    popover: {
                        className: 'guide-step-interactive',
                        title: title,
                        description: description,
                        position: 'center',
                        showButtons: ['next']
                    }
                }],
                onReset: () => {
                    // 继续原来的引导流程
                    setTimeout(() => {
                        this.continueGuide();
                    }, 300);
                }
            });

            tempDriver.drive();
        }
    }

    /**
     * 继续引导流程
     */
    continueGuide() {
        if (this.driver) {
            // 继续到下一步
            const nextBtn = document.querySelector('.driver-popover-next-btn');
            if (nextBtn) {
                nextBtn.click();
            }
        }
    }

    /**
     * 销毁引导实例
     */
    destroy() {
        // 清理交互式步骤的状态
        this.interactiveStepActive = false;
        document.querySelectorAll('.guide-menu-item-highlight').forEach(el => {
            el.classList.remove('guide-menu-item-highlight');
        });
        this.originalMenuClickHandlers.clear();

        if (this.driver) {
            this.driver.destroy(); // 使用destroy()方法
            this.driver = null;
        }
        
        this.stopPositionMonitoring(); // 停止位置监控
        this.isInitialized = false;
        console.log('🗑️ 用户引导系统已销毁');
    }
}

/**
 * 存储管理器 - 处理引导状态的持久化
 */
class GuideStorageManager {
    constructor(prefix = 'user_guide_') {
        this.prefix = prefix;
    }

    /**
     * 设置存储项
     */
    setItem(key, value) {
        try {
            localStorage.setItem(this.prefix + key, JSON.stringify(value));
            return true;
        } catch (error) {
            console.error('存储失败:', error);
            return false;
        }
    }

    /**
     * 获取存储项
     */
    getItem(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(this.prefix + key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('读取存储失败:', error);
            return defaultValue;
        }
    }

    /**
     * 删除存储项
     */
    removeItem(key) {
        try {
            localStorage.removeItem(this.prefix + key);
            return true;
        } catch (error) {
            console.error('删除存储失败:', error);
            return false;
        }
    }

    /**
     * 清空所有引导相关存储
     */
    clear() {
        try {
            const keys = Object.keys(localStorage).filter(key => key.startsWith(this.prefix));
            keys.forEach(key => localStorage.removeItem(key));
            return true;
        } catch (error) {
            console.error('清空存储失败:', error);
            return false;
        }
    }

    /**
     * 获取存储使用情况
     */
    getUsage() {
        const keys = Object.keys(localStorage).filter(key => key.startsWith(this.prefix));
        const totalSize = keys.reduce((size, key) => {
            return size + key.length + localStorage.getItem(key).length;
        }, 0);

        return {
            keys: keys.length,
            totalSize: totalSize,
            items: keys.map(key => ({
                key: key.replace(this.prefix, ''),
                size: key.length + localStorage.getItem(key).length
            }))
        };
    }
}

/**
 * 可访问性增强器 - 提供键盘导航和屏幕阅读器支持
 */
class AccessibilityEnhancer {
    constructor(userGuide) {
        this.userGuide = userGuide;
        this.keyboardHandlers = new Map();
        this.announcements = [];
        this.isActive = false;
    }

    /**
     * 初始化可访问性功能
     */
    init() {
        this.setupKeyboardNavigation();
        this.setupScreenReaderSupport();
        this.setupFocusManagement();
        console.log('✅ 可访问性增强功能已初始化');
    }

    /**
     * 设置键盘导航
     */
    setupKeyboardNavigation() {
        const keyHandler = (event) => {
            if (!this.isActive) return;

            switch (event.key) {
                case 'Escape':
                    event.preventDefault();
                    this.userGuide.skip();
                    this.announce('引导已跳过');
                    break;
                case 'ArrowRight':
                case 'Space':
                    event.preventDefault();
                    this.simulateButtonClick('next');
                    break;
                case 'ArrowLeft':
                    event.preventDefault();
                    this.simulateButtonClick('previous');
                    break;
                case 'Enter':
                    event.preventDefault();
                    this.simulateButtonClick('next');
                    break;
                case 'Tab':
                    // 允许Tab键在引导按钮间导航
                    this.manageFocusWithinPopover(event);
                    break;
            }
        };

        document.addEventListener('keydown', keyHandler);
        this.keyboardHandlers.set('main', keyHandler);
    }

    /**
     * 模拟按钮点击
     */
    simulateButtonClick(buttonType) {
        const selectors = {
            next: '.driver-popover-next-btn, .driver-popover-done-btn',
            previous: '.driver-popover-prev-btn',
            close: '.driver-popover-close-btn'
        };

        const button = document.querySelector(selectors[buttonType]);
        if (button && !button.disabled) {
            button.click();
            this.announce(`${buttonType === 'next' ? '下一步' : buttonType === 'previous' ? '上一步' : '关闭'}`);
        }
    }

    /**
     * 管理弹出框内的焦点
     */
    manageFocusWithinPopover(event) {
        const popover = document.querySelector('.driver-popover');
        if (!popover) return;

        const focusableElements = popover.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );

        if (focusableElements.length === 0) return;

        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (event.shiftKey) {
            // Shift + Tab
            if (document.activeElement === firstElement) {
                event.preventDefault();
                lastElement.focus();
            }
        } else {
            // Tab
            if (document.activeElement === lastElement) {
                event.preventDefault();
                firstElement.focus();
            }
        }
    }

    /**
     * 设置屏幕阅读器支持
     */
    setupScreenReaderSupport() {
        // 创建屏幕阅读器公告区域
        this.createAnnouncementRegion();

        // 监听引导事件
        document.addEventListener('userguide:onstepstart', (event) => {
            const { step } = event.detail;
            this.announce(`第 ${step} 步引导开始`);
        });

        document.addEventListener('userguide:onstephighlighted', () => {
            const popover = document.querySelector('.driver-popover');
            if (popover) {
                this.enhancePopoverAccessibility(popover);
            }
        });

        document.addEventListener('userguide:oncomplete', () => {
            this.announce('用户引导已完成');
            this.isActive = false;
        });
    }

    /**
     * 创建屏幕阅读器公告区域
     */
    createAnnouncementRegion() {
        if (document.getElementById('user-guide-announcements')) return;

        const announcer = document.createElement('div');
        announcer.id = 'user-guide-announcements';
        announcer.setAttribute('aria-live', 'polite');
        announcer.setAttribute('aria-atomic', 'true');
        announcer.style.cssText = `
            position: absolute;
            left: -10000px;
            width: 1px;
            height: 1px;
            overflow: hidden;
        `;
        document.body.appendChild(announcer);
    }

    /**
     * 增强弹出框的可访问性
     */
    enhancePopoverAccessibility(popover) {
        // 设置ARIA属性
        popover.setAttribute('role', 'dialog');
        popover.setAttribute('aria-modal', 'true');

        const title = popover.querySelector('.driver-popover-title');
        const description = popover.querySelector('.driver-popover-description');

        if (title) {
            title.id = title.id || 'guide-popover-title';
            popover.setAttribute('aria-labelledby', title.id);
        }

        if (description) {
            description.id = description.id || 'guide-popover-description';
            popover.setAttribute('aria-describedby', description.id);
        }

        // 设置焦点到第一个按钮
        setTimeout(() => {
            const firstButton = popover.querySelector('button');
            if (firstButton) {
                firstButton.focus();
            }
        }, 100);

        // 公告当前步骤内容
        if (title && description) {
            this.announce(`${title.textContent}. ${description.textContent}`);
        }
    }

    /**
     * 设置焦点管理
     */
    setupFocusManagement() {
        let previousFocus = null;

        document.addEventListener('userguide:onstart', () => {
            this.isActive = true;
            previousFocus = document.activeElement;
            this.announce('用户引导开始，使用方向键导航，ESC键退出');
        });

        document.addEventListener('userguide:oncomplete', () => {
            this.isActive = false;
            if (previousFocus && typeof previousFocus.focus === 'function') {
                previousFocus.focus();
            }
        });

        document.addEventListener('userguide:onskip', () => {
            this.isActive = false;
            if (previousFocus && typeof previousFocus.focus === 'function') {
                previousFocus.focus();
            }
        });
    }

    /**
     * 屏幕阅读器公告
     */
    announce(message) {
        const announcer = document.getElementById('user-guide-announcements');
        if (announcer) {
            announcer.textContent = message;
            this.announcements.push({
                message,
                timestamp: new Date().toISOString()
            });
        }
    }

    /**
     * 检查可访问性偏好
     */
    checkAccessibilityPreferences() {
        return {
            reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
            highContrast: window.matchMedia('(prefers-contrast: high)').matches,
            screenReader: this.detectScreenReader()
        };
    }

    /**
     * 检测屏幕阅读器
     */
    detectScreenReader() {
        // 简单的屏幕阅读器检测
        return !!(
            navigator.userAgent.match(/NVDA|JAWS|VoiceOver|TalkBack/i) ||
            window.speechSynthesis ||
            window.navigator.userAgent.includes('Accessibility')
        );
    }

    /**
     * 销毁可访问性功能
     */
    destroy() {
        this.keyboardHandlers.forEach((handler) => {
            document.removeEventListener('keydown', handler);
        });
        this.keyboardHandlers.clear();

        const announcer = document.getElementById('user-guide-announcements');
        if (announcer) {
            announcer.remove();
        }

        this.isActive = false;
        console.log('🗑️ 可访问性增强功能已销毁');
    }
}

// 全局实例
window.UserGuide = new UserGuideManager();
window.GuideStorage = new GuideStorageManager();
window.AccessibilityEnhancer = new AccessibilityEnhancer(window.UserGuide);

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { UserGuideManager, GuideStorageManager, AccessibilityEnhancer };
}
