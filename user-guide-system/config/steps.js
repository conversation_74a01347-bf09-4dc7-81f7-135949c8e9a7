/**
 * 用户引导步骤配置
 * 
 * 定义了完整的用户引导流程，包括欢迎、功能介绍、交互演示等
 * 
 * @version 2.0.0
 */

/**
 * 默认引导步骤配置
 */
export const defaultSteps = [
    {
        id: 'welcome',
        element: 'body',
        title: '👋 欢迎使用跨境运营助手！',
        description: '我将为您介绍平台的主要功能，帮助您快速上手。这个引导过程大约需要2-3分钟，您可以随时按 <strong>Escape</strong> 键退出，或者<strong>点击遮罩层任意位置</strong>进入下一步。',
        position: 'center',
        showButtons: ['next'],
        showProgress: true,
        className: 'guide-step-welcome',
        ariaLabel: '欢迎使用跨境运营助手',
        keyboardHint: '使用 Tab 键导航，Enter 键继续，Escape 键退出，或点击遮罩层进入下一步',
        metadata: {
            category: 'introduction',
            importance: 'high',
            estimatedTime: 10
        }
    },
    {
        id: 'sidebar-overview',
        element: '.sidebar',
        title: '📋 主导航菜单',
        description: '这里是主要的功能导航区域。您可以在这里访问<strong>仪表盘</strong>、<strong>产品库</strong>、<strong>建联记录</strong>等核心功能模块。<br><br><i class="fas fa-lightbulb text-yellow-500"></i> <strong>提示：</strong>点击遮罩层任意位置可以快速进入下一步！',
        position: 'right',
        showButtons: ['previous', 'next'],
        showProgress: true,
        className: 'guide-step-sidebar guide-step-navigation',
        ariaLabel: '主导航菜单介绍',
        keyboardHint: '使用方向键或 Tab 键导航，点击遮罩层或按 Enter 键继续',
        metadata: {
            category: 'navigation',
            importance: 'high',
            estimatedTime: 15
        }
    },
    {
        id: 'dashboard-menu',
        element: '.menu-item:first-child',
        title: '📊 仪表盘',
        description: '仪表盘提供了业务数据的总览，包括<strong>合作进度</strong>、<strong>邮件统计</strong>、<strong>收益分析</strong>等关键指标。<br><br><i class="fas fa-chart-line text-blue-500"></i> 在这里您可以快速了解业务表现和趋势。',
        position: 'right',
        showButtons: ['previous', 'next'],
        showProgress: true,
        className: 'guide-step-dashboard guide-step-feature',
        ariaLabel: '仪表盘功能介绍',
        keyboardHint: '点击遮罩层或使用键盘导航继续',
        metadata: {
            category: 'features',
            importance: 'medium',
            estimatedTime: 12
        }
    },
    {
        id: 'product-library',
        element: '.menu-item:nth-child(2)',
        title: '📦 产品库',
        description: '产品库用于管理您的商品信息。您可以添加产品详情、上传图片、设置价格等，为后续的推广合作做准备。',
        position: 'right',
        showButtons: ['previous', 'next'],
        showProgress: true,
        className: 'guide-step-products',
        ariaLabel: '产品库功能介绍',
        metadata: {
            category: 'features',
            importance: 'high',
            estimatedTime: 18
        }
    },
    {
        id: 'contact-records',
        element: '.menu-item:nth-child(3)',
        title: '👥 建联记录',
        description: '建联记录帮您管理与网红博主的合作关系。您可以查看联系历史、跟踪合作进度、管理邮件往来等。',
        position: 'right',
        showButtons: ['previous', 'next'],
        showProgress: true,
        className: 'guide-step-contacts',
        ariaLabel: '建联记录功能介绍',
        metadata: {
            category: 'features',
            importance: 'high',
            estimatedTime: 20
        }
    },
    {
        id: 'ai-assistant',
        element: '#ai-assistant-menu',
        title: '🤖 AI助手',
        description: '这是我们的<strong>核心功能</strong>！AI助手可以帮您：<br><br><i class="fas fa-search text-green-500"></i> <strong>分析产品</strong> - 智能解析商品特点<br><i class="fas fa-user-friends text-purple-500"></i> <strong>推荐博主</strong> - 匹配合适的合作伙伴<br><i class="fas fa-envelope text-blue-500"></i> <strong>生成邮件</strong> - 个性化沟通模板<br><br>大大提高您的工作效率！',
        position: 'right',
        showButtons: ['previous', 'next'],
        showProgress: true,
        className: 'guide-step-ai guide-step-feature',
        ariaLabel: 'AI助手功能介绍',
        keyboardHint: '这是重要功能，建议仔细了解。点击遮罩层继续',
        metadata: {
            category: 'features',
            importance: 'critical',
            estimatedTime: 25
        }
    },
    {
        id: 'ai-new-chat',
        element: '.new-chat',
        title: '💬 新建商品分析',
        description: '点击这里可以开始新的商品分析会话。只需粘贴商品链接，AI就会自动分析产品特点并推荐合适的合作博主。',
        position: 'right',
        showButtons: ['previous', 'next'],
        showProgress: true,
        className: 'guide-step-new-chat',
        ariaLabel: '新建商品分析功能',
        metadata: {
            category: 'ai-features',
            importance: 'high',
            estimatedTime: 15
        }
    },
    {
        id: 'main-content',
        element: '.main-content',
        title: '🖥️ 主工作区域',
        description: '这里是主要的工作区域，会根据您选择的功能显示不同的内容。目前显示的是AI助手界面，您可以在这里与AI进行对话。',
        position: 'left',
        showButtons: ['previous', 'next'],
        showProgress: true,
        className: 'guide-step-main-content',
        ariaLabel: '主工作区域介绍',
        metadata: {
            category: 'interface',
            importance: 'medium',
            estimatedTime: 12
        }
    },
    {
        id: 'user-profile',
        element: '#user-profile-sidebar',
        title: '👤 用户信息',
        description: '这里显示您的账户信息。点击可以访问<strong>账号设置</strong>、<strong>重新观看引导</strong>或<strong>退出登录</strong>。',
        position: 'top',
        showButtons: ['previous', 'next'],
        showProgress: true,
        className: 'guide-step-profile',
        ariaLabel: '用户信息区域介绍',
        metadata: {
            category: 'account',
            importance: 'medium',
            estimatedTime: 10
        }
    },
    {
        id: 'notifications',
        element: '.notification-container',
        title: '🔔 通知中心',
        description: '通知中心会显示重要的系统消息、合作进展更新、新邮件提醒等。红色数字表示未读通知数量。',
        position: 'bottom',
        showButtons: ['previous', 'next'],
        showProgress: true,
        className: 'guide-step-notifications',
        ariaLabel: '通知中心功能介绍',
        metadata: {
            category: 'interface',
            importance: 'medium',
            estimatedTime: 12
        }
    },
    {
        id: 'quick-start',
        element: '.central-input-container',
        title: '🚀 快速开始',
        description: '现在您可以尝试粘贴一个商品链接到这个输入框中，体验AI助手的强大功能。或者点击下方的<strong>"试用演示"</strong>按钮查看示例。',
        position: 'top',
        showButtons: ['previous', 'next'],
        showProgress: true,
        className: 'guide-step-quick-start',
        ariaLabel: '快速开始使用指导',
        metadata: {
            category: 'getting-started',
            importance: 'high',
            estimatedTime: 20
        }
    },
    {
        id: 'completion',
        element: 'body',
        title: '🎉 引导完成！',
        description: '恭喜您完成了平台功能介绍！现在您已经了解了所有主要功能。<br><br><i class="fas fa-redo text-blue-500"></i> 如果需要重新观看引导，可以点击用户头像中的<strong>"新手引导"</strong>选项。<br><br><i class="fas fa-heart text-red-500"></i> 祝您使用愉快！',
        position: 'center',
        showButtons: ['done'],
        showProgress: true,
        className: 'guide-step-complete',
        ariaLabel: '引导完成',
        keyboardHint: '按 Enter 键或点击完成按钮结束引导',
        metadata: {
            category: 'completion',
            importance: 'high',
            estimatedTime: 15
        }
    }
];

/**
 * 高级功能引导步骤
 */
export const advancedSteps = {
    'ai-assistant': [
        {
            id: 'ai-input-area',
            element: '.central-input-container',
            title: '📝 AI对话输入区',
            description: '在这里输入您的商品链接或描述，AI会自动分析并提供建议。支持多种格式的商品链接。',
            position: 'top',
            showButtons: ['previous', 'next'],
            showProgress: true
        },
        {
            id: 'ai-quick-actions',
            element: '.quick-prompt-btn',
            title: '⚡ 快速操作',
            description: '这些按钮提供了常用的操作模板，可以快速开始演示或使用预设的分析模式。',
            position: 'top',
            showButtons: ['previous', 'next'],
            showProgress: true
        }
    ],
    'product-library': [
        {
            id: 'add-product',
            element: '.add-product-btn',
            title: '➕ 添加产品',
            description: '点击这里添加新的产品到您的产品库中。',
            position: 'bottom',
            showButtons: ['previous', 'next'],
            showProgress: true
        }
    ]
};

/**
 * 交互式引导步骤
 */
export const interactiveSteps = [
    {
        id: 'interactive-sidebar',
        element: '.sidebar',
        title: '🎯 交互式体验',
        description: '请点击侧边栏中的任意菜单项，我将为您介绍该功能的详细信息。',
        position: 'right',
        showButtons: ['skip'],
        showProgress: true,
        interactive: true,
        targetElements: ['.menu-item'],
        onInteraction: 'showPageIntroduction'
    }
];

/**
 * 页面特定的介绍内容
 */
export const pageIntroductions = {
    'dashboard': {
        title: '📊 仪表盘详细介绍',
        description: '仪表盘为您提供业务数据的全面概览，包括合作统计、收益分析、趋势图表等。您可以通过这些数据了解业务表现并制定优化策略。',
        targetElement: '.main-content'
    },
    'products': {
        title: '📦 产品库详细介绍',
        description: '产品库是管理您所有商品的中心。您可以添加产品信息、上传图片、设置价格、管理库存，并为每个产品生成专属的推广策略。',
        targetElement: '.main-content'
    },
    'contacts': {
        title: '👥 建联记录详细介绍',
        description: '建联记录帮助您管理与网红博主的完整合作流程。从初次联系到合作完成，您可以跟踪每个阶段的进展并管理所有相关文档。',
        targetElement: '.main-content'
    },
    'ai-assistant': {
        title: '🤖 AI助手详细介绍',
        description: 'AI助手是我们的核心功能，它可以智能分析您的产品特点，推荐最适合的合作博主，并生成个性化的邮件模板，大大提高您的工作效率。',
        targetElement: '.main-content'
    }
};

/**
 * 步骤验证规则
 */
export const stepValidationRules = {
    // 元素存在性验证
    elementExists: [
        'sidebar', 'main-content', 'user-profile-sidebar', 
        'notification-container', 'central-input-container'
    ],
    
    // 可选元素（不存在时不显示对应步骤）
    optionalElements: [
        'ai-assistant-menu', 'new-chat', 'quick-prompt-btn'
    ],
    
    // 条件显示规则
    conditionalSteps: {
        'ai-new-chat': {
            condition: 'elementVisible',
            element: '.new-chat'
        },
        'notifications': {
            condition: 'elementVisible',
            element: '.notification-container'
        }
    }
};

/**
 * 主题特定的步骤样式
 */
export const themeStyles = {
    'dark': {
        overlayColor: 'rgba(0, 0, 0, 0.85)',
        highlightColor: '#42a5f5',
        textColor: '#ffffff'
    },
    'alien': {
        overlayColor: 'rgba(0, 0, 0, 0.8)',
        highlightColor: '#00ff88',
        textColor: '#00ff88'
    },
    'default': {
        overlayColor: 'rgba(0, 0, 0, 0.75)',
        highlightColor: '#2196f3',
        textColor: '#212121'
    }
};

/**
 * 无障碍访问配置
 */
export const accessibilityConfig = {
    // 屏幕阅读器公告模板
    announcements: {
        stepStart: '开始第 {current} 步，共 {total} 步：{title}',
        stepComplete: '完成第 {current} 步',
        guideComplete: '用户引导已完成',
        guideSkipped: '用户引导已跳过'
    },
    
    // 键盘导航提示
    keyboardHints: {
        navigation: '使用 Tab 键在按钮间导航',
        activation: '按 Enter 键或空格键激活按钮',
        exit: '按 Escape 键退出引导'
    },
    
    // ARIA 标签
    ariaLabels: {
        popover: '用户引导弹出框',
        progress: '引导进度',
        nextButton: '下一步',
        previousButton: '上一步',
        closeButton: '关闭引导',
        doneButton: '完成引导'
    }
};

/**
 * 性能优化配置
 */
export const performanceConfig = {
    // 最大步骤数限制
    maxSteps: 15,
    
    // 步骤预加载
    preloadSteps: 3,
    
    // 动画性能设置
    animation: {
        reducedMotion: {
            duration: 50,
            easing: 'linear'
        },
        normal: {
            duration: 300,
            easing: 'ease-in-out'
        }
    },
    
    // 内存管理
    cleanup: {
        // 自动清理超过此时间的步骤历史
        maxHistoryAge: 24 * 60 * 60 * 1000, // 24小时
        
        // 最大缓存的步骤数
        maxCachedSteps: 50
    }
};

/**
 * 导出所有配置
 */
export default {
    defaultSteps,
    advancedSteps,
    interactiveSteps,
    pageIntroductions,
    stepValidationRules,
    themeStyles,
    accessibilityConfig,
    performanceConfig
};
