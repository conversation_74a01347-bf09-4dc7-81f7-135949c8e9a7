/**
 * 核心引导管理器
 * 
 * 负责管理整个引导流程，协调各个模块的工作
 * 
 * @version 2.0.0
 */

import { StepConfigManager } from './StepConfigManager.js';
import { StateManager } from './StateManager.js';
import { EventManager } from './EventManager.js';
import { StorageManager } from '../modules/StorageManager.js';
import { AccessibilityEnhancer } from '../modules/AccessibilityEnhancer.js';
import { AnimationController } from '../modules/AnimationController.js';

/**
 * 核心引导管理器类
 */
export class GuideManager {
    constructor(config, errorHandler) {
        this.config = config;
        this.errorHandler = errorHandler;
        
        // 初始化状态
        this.isInitialized = false;
        this.isActive = false;
        this.isPaused = false;
        this.currentStepIndex = 0;
        
        // Driver.js 实例
        this.driverInstance = null;
        
        // 子模块
        this.stepConfigManager = null;
        this.stateManager = null;
        this.eventManager = null;
        this.storageManager = null;
        this.accessibilityEnhancer = null;
        this.animationController = null;
        
        // 绑定方法上下文
        this.handleStepChange = this.handleStepChange.bind(this);
        this.handleComplete = this.handleComplete.bind(this);
        this.handleSkip = this.handleSkip.bind(this);
        this.handleError = this.handleError.bind(this);
    }

    /**
     * 初始化管理器
     * @returns {Promise<boolean>}
     */
    async init() {
        try {
            console.log('🔧 初始化核心引导管理器...');
            
            // 初始化事件管理器
            this.eventManager = new EventManager();
            
            // 初始化存储管理器
            this.storageManager = new StorageManager(this.config.storage);
            await this.storageManager.init();
            
            // 初始化状态管理器
            this.stateManager = new StateManager(this.storageManager, this.config);
            await this.stateManager.init();
            
            // 初始化步骤配置管理器
            this.stepConfigManager = new StepConfigManager(this.config);
            await this.stepConfigManager.init();
            
            // 初始化无障碍增强器
            if (this.config.accessibility.enabled) {
                this.accessibilityEnhancer = new AccessibilityEnhancer(this.config.accessibility);
                await this.accessibilityEnhancer.init();
            }
            
            // 初始化动画控制器
            if (this.config.animation.enabled) {
                this.animationController = new AnimationController(this.config.animation);
                await this.animationController.init();
            }
            
            // 设置事件监听
            this.setupEventListeners();
            
            this.isInitialized = true;
            console.log('✅ 核心引导管理器初始化完成');
            
            return true;
            
        } catch (error) {
            this.errorHandler?.handleError(error, 'GuideManager.init');
            console.error('❌ 核心引导管理器初始化失败:', error);
            return false;
        }
    }

    /**
     * 设置事件监听
     * @private
     */
    setupEventListeners() {
        // 监听窗口大小变化
        window.addEventListener('resize', this.handleResize.bind(this));
        
        // 监听键盘事件（无障碍访问）
        if (this.config.accessibility.keyboardNavigation) {
            document.addEventListener('keydown', this.handleKeydown.bind(this));
        }
        
        // 监听主题变化
        if (window.matchMedia) {
            const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
            darkModeQuery.addEventListener('change', this.handleThemeChange.bind(this));
        }
    }

    /**
     * 开始引导
     * @param {string} [stepId] - 可选的起始步骤ID
     * @returns {Promise<boolean>}
     */
    async start(stepId = null) {
        try {
            if (!this.isInitialized) {
                throw new Error('管理器未初始化');
            }
            
            if (this.isActive) {
                console.warn('⚠️ 引导已经在运行中');
                return false;
            }
            
            console.log('🚀 开始用户引导...');
            
            // 获取引导步骤
            const steps = await this.stepConfigManager.getSteps();
            if (!steps || steps.length === 0) {
                throw new Error('没有可用的引导步骤');
            }
            
            // 确定起始步骤
            let startIndex = 0;
            if (stepId) {
                const stepIndex = steps.findIndex(step => step.id === stepId);
                if (stepIndex !== -1) {
                    startIndex = stepIndex;
                }
            }
            
            // 创建 Driver.js 实例
            await this.createDriverInstance(steps, startIndex);
            
            // 更新状态
            this.isActive = true;
            this.isPaused = false;
            this.currentStepIndex = startIndex;
            
            // 保存状态
            await this.stateManager.saveState({
                isActive: true,
                currentStepIndex: startIndex,
                startTime: new Date().toISOString()
            });
            
            // 启用无障碍增强
            if (this.accessibilityEnhancer) {
                this.accessibilityEnhancer.enable();
            }
            
            // 启动 Driver.js
            this.driverInstance.drive();
            
            // 触发开始事件
            this.eventManager.emit('guide:start', {
                totalSteps: steps.length,
                startIndex: startIndex,
                startTime: new Date().toISOString()
            });
            
            console.log(`✅ 引导已开始 (${steps.length} 步骤)`);
            return true;
            
        } catch (error) {
            this.errorHandler?.handleError(error, 'GuideManager.start');
            console.error('❌ 开始引导失败:', error);
            return false;
        }
    }

    /**
     * 创建 Driver.js 实例
     * @param {Array} steps - 引导步骤
     * @param {number} startIndex - 起始索引
     * @private
     */
    async createDriverInstance(steps, startIndex = 0) {
        const DriverConstructor = window.driver?.driver || window.Driver;
        if (!DriverConstructor) {
            throw new Error('Driver.js 不可用');
        }

        // 构建 Driver.js 配置
        const driverConfig = {
            className: 'user-guide-system-v2',
            animate: this.config.animation.enabled,
            opacity: this.parseOpacity(this.config.overlay.color),
            padding: 15,
            allowClose: true,
            overlayClickNext: true, // 启用点击遮罩层进入下一步
            showProgress: true,
            progressText: '<i class="fas fa-route"></i> 第 {{current}} 步，共 {{total}} 步',

            // 按钮文本 - 添加 Font Awesome 图标
            doneBtnText: '<i class="fas fa-check"></i> 完成引导',
            closeBtnText: '<i class="fas fa-times"></i>',
            nextBtnText: '<i class="fas fa-arrow-right"></i> 下一步',
            prevBtnText: '<i class="fas fa-arrow-left"></i> 上一步',

            // 步骤配置
            steps: this.transformStepsForDriver(steps),

            // 事件回调
            onHighlighted: this.handleStepChange,
            onDeselected: this.handleStepDeselected.bind(this),
            onReset: this.handleComplete,
            onDestroyed: this.handleSkip,

            // 新增：遮罩层点击回调
            onOverlayClick: this.handleOverlayClick.bind(this)
        };

        // 创建实例
        this.driverInstance = DriverConstructor(driverConfig);

        // 添加自定义事件监听
        this.setupCustomEventListeners();

        // 如果有起始索引，设置当前步骤
        if (startIndex > 0) {
            this.currentStepIndex = startIndex;
        }
    }

    /**
     * 转换步骤格式为 Driver.js 格式
     * @param {Array} steps - 原始步骤
     * @returns {Array} Driver.js 格式的步骤
     * @private
     */
    transformStepsForDriver(steps) {
        return steps.map((step, index) => {
            // 提取步骤图标
            const iconMatch = step.title.match(/^([^\s]+)\s+(.+)$/);
            const icon = iconMatch ? iconMatch[1] : '';
            const titleText = iconMatch ? iconMatch[2] : step.title;

            // 构建带图标的标题
            const titleWithIcon = icon ?
                `<div class="flex items-center gap-3">
                    <i class="fas ${this.getIconClass(icon)}"></i>
                    <span class="title-text">${titleText}</span>
                </div>` :
                `<span class="title-text">${step.title}</span>`;

            // 构建描述，添加键盘提示
            let description = step.description;
            if (this.config.accessibility.keyboardNavigation && step.keyboardHint) {
                description += `<div class="keyboard-hint">
                    <i class="fas fa-keyboard"></i> ${step.keyboardHint}
                </div>`;
            }

            return {
                element: step.element,
                popover: {
                    className: `guide-step-${step.id || index} ${step.className || ''}`,
                    title: titleWithIcon,
                    description: description,
                    position: step.position || 'auto',
                    showButtons: step.showButtons || ['previous', 'next'],
                    showProgress: step.showProgress !== false,
                    // 添加自定义属性
                    stepId: step.id,
                    stepType: step.metadata?.category || 'default'
                }
            };
        });
    }

    /**
     * 获取图标的 CSS 类名
     * @param {string} emoji - emoji 图标
     * @returns {string} Font Awesome 类名
     * @private
     */
    getIconClass(emoji) {
        const iconMap = {
            '👋': 'fa-hand-wave',
            '📋': 'fa-clipboard-list',
            '📊': 'fa-chart-bar',
            '📦': 'fa-box',
            '👥': 'fa-users',
            '🤖': 'fa-robot',
            '💬': 'fa-comments',
            '🖥️': 'fa-desktop',
            '👤': 'fa-user',
            '🔔': 'fa-bell',
            '🚀': 'fa-rocket',
            '🎉': 'fa-party-horn',
            '🎯': 'fa-bullseye',
            '📝': 'fa-edit',
            '⚡': 'fa-bolt',
            '➕': 'fa-plus'
        };

        return iconMap[emoji] || 'fa-info-circle';
    }

    /**
     * 解析透明度值
     * @param {string} color - 颜色字符串
     * @returns {number} 透明度值
     * @private
     */
    parseOpacity(color) {
        const match = color.match(/rgba?\([^,]+,[^,]+,[^,]+,\s*([^)]+)\)/);
        return match ? parseFloat(match[1]) : 0.75;
    }

    /**
     * 设置自定义事件监听
     * @private
     */
    setupCustomEventListeners() {
        // 监听遮罩层点击事件
        document.addEventListener('click', this.handleDocumentClick.bind(this));

        // 监听弹出框创建事件
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE &&
                        node.classList?.contains('driver-popover')) {
                        this.enhancePopover(node);
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        this.popoverObserver = observer;
    }

    /**
     * 增强弹出框
     * @param {Element} popover - 弹出框元素
     * @private
     */
    enhancePopover(popover) {
        try {
            // 添加跳过按钮
            this.addSkipButton(popover);

            // 添加遮罩层点击提示
            this.addOverlayClickHint();

            // 增强无障碍访问
            this.enhanceAccessibility(popover);

        } catch (error) {
            this.errorHandler?.handleError(error, 'GuideManager.enhancePopover');
        }
    }

    /**
     * 添加跳过按钮
     * @param {Element} popover - 弹出框元素
     * @private
     */
    addSkipButton(popover) {
        const existingCloseBtn = popover.querySelector('.driver-popover-close-btn');
        if (existingCloseBtn) {
            existingCloseBtn.innerHTML = '<i class="fas fa-times"></i>';
            existingCloseBtn.setAttribute('aria-label', '跳过引导');
            existingCloseBtn.setAttribute('title', '跳过引导');
            return;
        }

        const closeBtn = document.createElement('button');
        closeBtn.className = 'driver-popover-close-btn';
        closeBtn.innerHTML = '<i class="fas fa-times"></i>';
        closeBtn.setAttribute('aria-label', '跳过引导');
        closeBtn.setAttribute('title', '跳过引导');
        closeBtn.addEventListener('click', () => {
            this.skip();
        });

        popover.appendChild(closeBtn);
    }

    /**
     * 添加遮罩层点击提示
     * @private
     */
    addOverlayClickHint() {
        const overlay = document.querySelector('.driver-overlay');
        if (!overlay) return;

        // 移除已存在的提示
        const existingHint = overlay.querySelector('.user-guide-overlay-hint');
        if (existingHint) {
            existingHint.remove();
        }

        // 添加新的提示
        const hint = document.createElement('div');
        hint.className = 'user-guide-overlay-hint';
        hint.innerHTML = '<i class="fas fa-mouse-pointer"></i> 点击任意位置继续';

        overlay.appendChild(hint);

        // 3秒后自动隐藏提示
        setTimeout(() => {
            if (hint.parentNode) {
                hint.remove();
            }
        }, 3000);
    }

    /**
     * 增强无障碍访问
     * @param {Element} popover - 弹出框元素
     * @private
     */
    enhanceAccessibility(popover) {
        // 设置 ARIA 属性
        popover.setAttribute('role', 'dialog');
        popover.setAttribute('aria-modal', 'true');
        popover.setAttribute('aria-labelledby', 'guide-title');
        popover.setAttribute('aria-describedby', 'guide-description');

        // 设置标题和描述的 ID
        const title = popover.querySelector('.driver-popover-title');
        const description = popover.querySelector('.driver-popover-description');

        if (title) {
            title.id = 'guide-title';
        }

        if (description) {
            description.id = 'guide-description';
        }

        // 管理焦点
        if (this.accessibilityEnhancer) {
            this.accessibilityEnhancer.manageFocus(popover);
        }
    }

    /**
     * 处理文档点击事件
     * @param {Event} event - 点击事件
     * @private
     */
    handleDocumentClick(event) {
        // 只在引导激活时处理
        if (!this.isActive) return;

        const overlay = document.querySelector('.driver-overlay');
        if (overlay && event.target === overlay) {
            this.handleOverlayClick(event);
        }
    }

    /**
     * 处理遮罩层点击
     * @param {Event} event - 点击事件
     * @private
     */
    handleOverlayClick(event) {
        try {
            // 防止在动画期间重复点击
            if (this.isTransitioning) return;

            this.isTransitioning = true;

            // 添加点击效果
            this.addClickEffect(event);

            // 延迟执行下一步，让用户看到点击效果
            setTimeout(() => {
                this.next();
                this.isTransitioning = false;
            }, 150);

        } catch (error) {
            this.errorHandler?.handleError(error, 'GuideManager.handleOverlayClick');
            this.isTransitioning = false;
        }
    }

    /**
     * 添加点击效果
     * @param {Event} event - 点击事件
     * @private
     */
    addClickEffect(event) {
        const overlay = event.target;
        const ripple = document.createElement('div');

        ripple.className = 'user-guide-click-hint';
        ripple.style.left = event.clientX + 'px';
        ripple.style.top = event.clientY + 'px';
        ripple.innerHTML = '<i class="fas fa-hand-pointer text-blue-500 text-2xl"></i>';

        overlay.appendChild(ripple);

        // 移除效果
        setTimeout(() => {
            if (ripple.parentNode) {
                ripple.remove();
            }
        }, 1000);
    }

    /**
     * 处理步骤变化
     * @param {Object} element - 当前高亮元素
     * @private
     */
    handleStepChange(element) {
        try {
            const stepIndex = this.driverInstance.getActiveIndex();
            this.currentStepIndex = stepIndex;

            // 更新状态
            this.stateManager.saveState({
                currentStepIndex: stepIndex,
                lastUpdateTime: new Date().toISOString()
            });

            // 无障碍增强
            if (this.accessibilityEnhancer) {
                this.accessibilityEnhancer.announceStep(stepIndex + 1, this.getTotalSteps());
                this.accessibilityEnhancer.manageFocus(element);
            }

            // 触发步骤变化事件
            this.eventManager.emit('guide:step:change', {
                stepIndex: stepIndex,
                element: element,
                progress: this.getProgress()
            });

        } catch (error) {
            this.errorHandler?.handleError(error, 'GuideManager.handleStepChange');
        }
    }

    /**
     * 处理步骤取消选择
     * @param {Object} element - 取消选择的元素
     * @private
     */
    handleStepDeselected(element) {
        // 可以在这里添加步骤取消选择的逻辑
    }

    /**
     * 处理引导完成
     * @private
     */
    handleComplete() {
        try {
            console.log('🎉 用户引导完成');
            
            // 更新状态
            this.isActive = false;
            this.isPaused = false;
            
            // 保存完成状态
            this.stateManager.markAsCompleted();
            
            // 禁用无障碍增强
            if (this.accessibilityEnhancer) {
                this.accessibilityEnhancer.disable();
            }
            
            // 触发完成事件
            this.eventManager.emit('guide:complete', {
                completedAt: new Date().toISOString(),
                totalSteps: this.getTotalSteps(),
                completedSteps: this.currentStepIndex + 1
            });
            
        } catch (error) {
            this.errorHandler?.handleError(error, 'GuideManager.handleComplete');
        }
    }

    /**
     * 处理引导跳过
     * @private
     */
    handleSkip() {
        try {
            console.log('⏭️ 用户跳过引导');
            
            // 更新状态
            this.isActive = false;
            this.isPaused = false;
            
            // 保存跳过状态
            this.stateManager.saveState({
                isActive: false,
                skipped: true,
                skippedAt: new Date().toISOString()
            });
            
            // 禁用无障碍增强
            if (this.accessibilityEnhancer) {
                this.accessibilityEnhancer.disable();
            }
            
            // 触发跳过事件
            this.eventManager.emit('guide:skip', {
                skippedAt: new Date().toISOString(),
                currentStep: this.currentStepIndex,
                totalSteps: this.getTotalSteps()
            });
            
        } catch (error) {
            this.errorHandler?.handleError(error, 'GuideManager.handleSkip');
        }
    }

    /**
     * 处理错误
     * @param {Error} error - 错误对象
     * @private
     */
    handleError(error) {
        this.errorHandler?.handleError(error, 'GuideManager.driverError');
        
        // 触发错误事件
        this.eventManager.emit('guide:error', {
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 处理窗口大小变化
     * @private
     */
    handleResize() {
        if (this.isActive && this.driverInstance) {
            // 重新定位弹出框
            setTimeout(() => {
                this.driverInstance.refresh();
            }, 100);
        }
    }

    /**
     * 处理键盘事件
     * @param {KeyboardEvent} event - 键盘事件
     * @private
     */
    handleKeydown(event) {
        if (!this.isActive) return;
        
        switch (event.key) {
            case 'Escape':
                event.preventDefault();
                this.skip();
                break;
            case 'ArrowRight':
            case 'ArrowDown':
                if (event.ctrlKey || event.metaKey) {
                    event.preventDefault();
                    this.next();
                }
                break;
            case 'ArrowLeft':
            case 'ArrowUp':
                if (event.ctrlKey || event.metaKey) {
                    event.preventDefault();
                    this.previous();
                }
                break;
        }
    }

    /**
     * 处理主题变化
     * @param {MediaQueryListEvent} event - 主题变化事件
     * @private
     */
    handleThemeChange(event) {
        if (this.isActive) {
            // 可以在这里添加主题变化的处理逻辑
            console.log('🎨 检测到主题变化:', event.matches ? 'dark' : 'light');
        }
    }

    // ==================== 公共方法 ====================

    /**
     * 重新开始引导
     * @returns {Promise<boolean>}
     */
    async restart() {
        if (this.isActive) {
            this.skip();
        }
        
        // 重置状态
        await this.stateManager.reset();
        
        // 重新开始
        return await this.start();
    }

    /**
     * 暂停引导
     */
    pause() {
        if (this.isActive && !this.isPaused) {
            this.isPaused = true;
            
            // 隐藏当前弹出框
            if (this.driverInstance) {
                // Driver.js 没有直接的暂停方法，我们可以隐藏弹出框
                const popover = document.querySelector('.driver-popover');
                if (popover) {
                    popover.style.display = 'none';
                }
            }
            
            this.eventManager.emit('guide:pause', {
                pausedAt: new Date().toISOString(),
                currentStep: this.currentStepIndex
            });
        }
    }

    /**
     * 恢复引导
     */
    resume() {
        if (this.isActive && this.isPaused) {
            this.isPaused = false;
            
            // 显示当前弹出框
            if (this.driverInstance) {
                const popover = document.querySelector('.driver-popover');
                if (popover) {
                    popover.style.display = 'block';
                }
            }
            
            this.eventManager.emit('guide:resume', {
                resumedAt: new Date().toISOString(),
                currentStep: this.currentStepIndex
            });
        }
    }

    /**
     * 跳过引导
     */
    skip() {
        if (this.driverInstance) {
            this.driverInstance.destroy();
        }
    }

    /**
     * 下一步
     */
    next() {
        if (this.driverInstance && this.isActive && !this.isPaused) {
            this.driverInstance.moveNext();
        }
    }

    /**
     * 上一步
     */
    previous() {
        if (this.driverInstance && this.isActive && !this.isPaused) {
            this.driverInstance.movePrevious();
        }
    }

    /**
     * 销毁管理器
     */
    destroy() {
        try {
            // 停止引导
            if (this.isActive) {
                this.skip();
            }

            // 移除事件监听
            window.removeEventListener('resize', this.handleResize);
            document.removeEventListener('keydown', this.handleKeydown);
            document.removeEventListener('click', this.handleDocumentClick);

            // 断开弹出框观察器
            if (this.popoverObserver) {
                this.popoverObserver.disconnect();
                this.popoverObserver = null;
            }

            // 清理遮罩层提示
            const overlayHints = document.querySelectorAll('.user-guide-overlay-hint');
            overlayHints.forEach(hint => hint.remove());

            // 清理点击效果
            const clickHints = document.querySelectorAll('.user-guide-click-hint');
            clickHints.forEach(hint => hint.remove());

            // 销毁子模块
            this.accessibilityEnhancer?.destroy();
            this.animationController?.destroy();
            this.eventManager?.destroy();

            // 重置状态
            this.isInitialized = false;
            this.isActive = false;
            this.isTransitioning = false;
            this.driverInstance = null;

        } catch (error) {
            this.errorHandler?.handleError(error, 'GuideManager.destroy');
        }
    }

    // ==================== 状态查询方法 ====================

    /**
     * 检查是否激活
     * @returns {boolean}
     */
    isActive() {
        return this.isActive;
    }

    /**
     * 获取当前步骤
     * @returns {Object|null}
     */
    getCurrentStep() {
        if (!this.stepConfigManager) return null;
        
        const steps = this.stepConfigManager.getSteps();
        return steps[this.currentStepIndex] || null;
    }

    /**
     * 获取进度信息
     * @returns {Object}
     */
    getProgress() {
        const total = this.getTotalSteps();
        const current = this.currentStepIndex + 1;
        
        return {
            current: current,
            total: total,
            percentage: total > 0 ? Math.round((current / total) * 100) : 0
        };
    }

    /**
     * 获取总步骤数
     * @returns {number}
     */
    getTotalSteps() {
        if (!this.stepConfigManager) return 0;
        
        const steps = this.stepConfigManager.getSteps();
        return steps ? steps.length : 0;
    }

    /**
     * 检查是否已完成
     * @returns {boolean}
     */
    isCompleted() {
        return this.stateManager?.isCompleted() || false;
    }

    /**
     * 检查是否应该显示引导
     * @returns {boolean}
     */
    shouldShowGuide() {
        return this.stateManager?.shouldShowGuide() || false;
    }

    /**
     * 重置状态
     */
    reset() {
        this.stateManager?.reset();
    }

    /**
     * 标记为已完成
     */
    markAsCompleted() {
        this.stateManager?.markAsCompleted();
    }

    /**
     * 获取统计信息
     * @returns {Object}
     */
    getStats() {
        return this.stateManager?.getStats() || {};
    }

    // ==================== 事件系统代理 ====================

    /**
     * 监听事件
     * @param {string} eventName - 事件名称
     * @param {Function} callback - 回调函数
     */
    on(eventName, callback) {
        this.eventManager?.on(eventName, callback);
    }

    /**
     * 移除事件监听
     * @param {string} eventName - 事件名称
     * @param {Function} callback - 回调函数
     */
    off(eventName, callback) {
        this.eventManager?.off(eventName, callback);
    }
}
