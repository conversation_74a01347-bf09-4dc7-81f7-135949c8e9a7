/**
 * 步骤配置管理器
 * 
 * 负责管理引导步骤的配置、验证和动态加载
 * 
 * @version 2.0.0
 */

import { defaultSteps } from '../config/steps.js';

/**
 * 步骤配置管理器类
 */
export class StepConfigManager {
    constructor(config) {
        this.config = config;
        this.steps = [];
        this.customSteps = new Map();
        this.isInitialized = false;
    }

    /**
     * 初始化步骤配置管理器
     * @returns {Promise<boolean>}
     */
    async init() {
        try {
            console.log('📋 初始化步骤配置管理器...');
            
            // 加载默认步骤
            await this.loadDefaultSteps();
            
            // 验证步骤配置
            this.validateSteps();
            
            // 优化步骤顺序
            this.optimizeStepOrder();
            
            this.isInitialized = true;
            console.log(`✅ 步骤配置管理器初始化完成 (${this.steps.length} 个步骤)`);
            
            return true;
            
        } catch (error) {
            console.error('❌ 步骤配置管理器初始化失败:', error);
            return false;
        }
    }

    /**
     * 加载默认步骤
     * @private
     */
    async loadDefaultSteps() {
        try {
            // 使用导入的默认步骤
            this.steps = [...defaultSteps];
            
            // 应用配置过滤
            this.applyConfigFilters();
            
        } catch (error) {
            console.error('❌ 加载默认步骤失败:', error);
            // 使用备用步骤
            this.steps = this.getFallbackSteps();
        }
    }

    /**
     * 应用配置过滤
     * @private
     */
    applyConfigFilters() {
        // 根据配置限制最大步骤数
        if (this.config.performance.maxSteps && this.steps.length > this.config.performance.maxSteps) {
            console.warn(`⚠️ 步骤数量超过限制 (${this.steps.length} > ${this.config.performance.maxSteps})，将截取前 ${this.config.performance.maxSteps} 个步骤`);
            this.steps = this.steps.slice(0, this.config.performance.maxSteps);
        }
        
        // 过滤不可用的元素
        this.steps = this.steps.filter(step => {
            if (step.element === 'body') return true;
            
            const element = document.querySelector(step.element);
            if (!element) {
                console.warn(`⚠️ 步骤元素不存在: ${step.element}`);
                return false;
            }
            
            // 检查元素是否可见
            const rect = element.getBoundingClientRect();
            if (rect.width === 0 && rect.height === 0) {
                console.warn(`⚠️ 步骤元素不可见: ${step.element}`);
                return false;
            }
            
            return true;
        });
    }

    /**
     * 验证步骤配置
     * @private
     */
    validateSteps() {
        const validSteps = [];
        
        this.steps.forEach((step, index) => {
            try {
                // 验证必需字段
                if (!step.id) {
                    step.id = `step-${index}`;
                }
                
                if (!step.title || !step.description) {
                    console.warn(`⚠️ 步骤 ${step.id} 缺少标题或描述`);
                    return;
                }
                
                if (!step.element) {
                    console.warn(`⚠️ 步骤 ${step.id} 缺少目标元素`);
                    return;
                }
                
                // 验证元素选择器
                if (step.element !== 'body') {
                    try {
                        document.querySelector(step.element);
                    } catch (error) {
                        console.warn(`⚠️ 步骤 ${step.id} 的元素选择器无效: ${step.element}`);
                        return;
                    }
                }
                
                // 设置默认值
                step.position = step.position || 'auto';
                step.showButtons = step.showButtons || ['previous', 'next'];
                step.showProgress = step.showProgress !== false;
                
                // 验证位置
                const validPositions = ['auto', 'top', 'bottom', 'left', 'right', 'center'];
                if (!validPositions.includes(step.position)) {
                    console.warn(`⚠️ 步骤 ${step.id} 的位置无效: ${step.position}，使用默认值 'auto'`);
                    step.position = 'auto';
                }
                
                // 验证按钮配置
                if (!Array.isArray(step.showButtons)) {
                    step.showButtons = ['previous', 'next'];
                }
                
                // 无障碍增强
                this.enhanceStepAccessibility(step);
                
                validSteps.push(step);
                
            } catch (error) {
                console.error(`❌ 验证步骤 ${step.id || index} 时发生错误:`, error);
            }
        });
        
        this.steps = validSteps;
        
        if (this.steps.length === 0) {
            console.warn('⚠️ 没有有效的引导步骤，使用备用步骤');
            this.steps = this.getFallbackSteps();
        }
    }

    /**
     * 增强步骤的无障碍访问
     * @param {Object} step - 步骤对象
     * @private
     */
    enhanceStepAccessibility(step) {
        // 确保标题和描述符合无障碍要求
        if (step.title.length > 100) {
            console.warn(`⚠️ 步骤 ${step.id} 标题过长，建议控制在100字符以内`);
        }
        
        if (step.description.length > 300) {
            console.warn(`⚠️ 步骤 ${step.id} 描述过长，建议控制在300字符以内`);
        }
        
        // 添加 ARIA 标签
        step.ariaLabel = step.ariaLabel || step.title;
        step.ariaDescription = step.ariaDescription || step.description;
        
        // 确保有键盘导航提示
        if (this.config.accessibility.keyboardNavigation) {
            if (!step.description.includes('键盘') && !step.keyboardHint) {
                step.keyboardHint = '使用 Tab 键导航，Enter 键确认，Escape 键退出';
            }
        }
    }

    /**
     * 优化步骤顺序
     * @private
     */
    optimizeStepOrder() {
        // 按照页面布局优化步骤顺序
        this.steps.sort((a, b) => {
            // 'body' 元素的步骤优先
            if (a.element === 'body' && b.element !== 'body') return -1;
            if (b.element === 'body' && a.element !== 'body') return 1;
            
            // 按照元素在页面中的位置排序
            if (a.element !== 'body' && b.element !== 'body') {
                try {
                    const elementA = document.querySelector(a.element);
                    const elementB = document.querySelector(b.element);
                    
                    if (elementA && elementB) {
                        const rectA = elementA.getBoundingClientRect();
                        const rectB = elementB.getBoundingClientRect();
                        
                        // 按照从上到下，从左到右的顺序
                        if (Math.abs(rectA.top - rectB.top) > 50) {
                            return rectA.top - rectB.top;
                        } else {
                            return rectA.left - rectB.left;
                        }
                    }
                } catch (error) {
                    // 如果获取元素位置失败，保持原顺序
                }
            }
            
            return 0;
        });
        
        // 重新分配步骤ID以确保顺序
        this.steps.forEach((step, index) => {
            step.order = index;
        });
    }

    /**
     * 获取备用步骤
     * @returns {Array} 备用步骤数组
     * @private
     */
    getFallbackSteps() {
        return [
            {
                id: 'welcome',
                element: 'body',
                title: '👋 欢迎使用跨境运营助手！',
                description: '我将为您介绍平台的主要功能，帮助您快速上手。',
                position: 'center',
                showButtons: ['next'],
                showProgress: true
            },
            {
                id: 'sidebar',
                element: '.sidebar',
                title: '📋 主导航菜单',
                description: '这里是主要的功能导航区域，包含仪表盘、产品库、建联记录等功能模块。',
                position: 'right',
                showButtons: ['previous', 'next'],
                showProgress: true
            },
            {
                id: 'complete',
                element: 'body',
                title: '🎉 引导完成！',
                description: '您已经了解了平台的基本功能。如需更多帮助，请点击用户头像中的"新手引导"。',
                position: 'center',
                showButtons: ['done'],
                showProgress: true
            }
        ];
    }

    // ==================== 公共方法 ====================

    /**
     * 获取所有步骤
     * @returns {Array} 步骤数组
     */
    getSteps() {
        return [...this.steps];
    }

    /**
     * 根据ID获取步骤
     * @param {string} stepId - 步骤ID
     * @returns {Object|null} 步骤对象
     */
    getStepById(stepId) {
        return this.steps.find(step => step.id === stepId) || null;
    }

    /**
     * 根据索引获取步骤
     * @param {number} index - 步骤索引
     * @returns {Object|null} 步骤对象
     */
    getStepByIndex(index) {
        return this.steps[index] || null;
    }

    /**
     * 添加自定义步骤
     * @param {Object} step - 步骤对象
     * @param {number} [index] - 插入位置，默认添加到末尾
     * @returns {boolean} 是否添加成功
     */
    addStep(step, index = null) {
        try {
            // 验证步骤
            if (!step.id || !step.title || !step.description || !step.element) {
                console.error('❌ 步骤缺少必需字段');
                return false;
            }
            
            // 检查ID是否重复
            if (this.getStepById(step.id)) {
                console.error(`❌ 步骤ID已存在: ${step.id}`);
                return false;
            }
            
            // 增强无障碍访问
            this.enhanceStepAccessibility(step);
            
            // 添加步骤
            if (index !== null && index >= 0 && index <= this.steps.length) {
                this.steps.splice(index, 0, step);
            } else {
                this.steps.push(step);
            }
            
            // 重新优化顺序
            this.optimizeStepOrder();
            
            console.log(`✅ 添加自定义步骤: ${step.id}`);
            return true;
            
        } catch (error) {
            console.error('❌ 添加步骤失败:', error);
            return false;
        }
    }

    /**
     * 移除步骤
     * @param {string} stepId - 步骤ID
     * @returns {boolean} 是否移除成功
     */
    removeStep(stepId) {
        try {
            const index = this.steps.findIndex(step => step.id === stepId);
            if (index === -1) {
                console.warn(`⚠️ 未找到步骤: ${stepId}`);
                return false;
            }
            
            this.steps.splice(index, 1);
            
            // 重新优化顺序
            this.optimizeStepOrder();
            
            console.log(`✅ 移除步骤: ${stepId}`);
            return true;
            
        } catch (error) {
            console.error('❌ 移除步骤失败:', error);
            return false;
        }
    }

    /**
     * 更新步骤
     * @param {string} stepId - 步骤ID
     * @param {Object} updates - 更新内容
     * @returns {boolean} 是否更新成功
     */
    updateStep(stepId, updates) {
        try {
            const step = this.getStepById(stepId);
            if (!step) {
                console.warn(`⚠️ 未找到步骤: ${stepId}`);
                return false;
            }
            
            // 应用更新
            Object.assign(step, updates);
            
            // 重新验证
            this.enhanceStepAccessibility(step);
            
            console.log(`✅ 更新步骤: ${stepId}`);
            return true;
            
        } catch (error) {
            console.error('❌ 更新步骤失败:', error);
            return false;
        }
    }

    /**
     * 获取步骤统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            totalSteps: this.steps.length,
            interactiveSteps: this.steps.filter(step => step.element !== 'body').length,
            centerSteps: this.steps.filter(step => step.position === 'center').length,
            customSteps: this.customSteps.size,
            averageTitleLength: this.steps.reduce((sum, step) => sum + step.title.length, 0) / this.steps.length,
            averageDescriptionLength: this.steps.reduce((sum, step) => sum + step.description.length, 0) / this.steps.length
        };
    }

    /**
     * 验证所有步骤元素是否存在
     * @returns {Object} 验证结果
     */
    validateElements() {
        const results = {
            valid: [],
            invalid: [],
            hidden: []
        };
        
        this.steps.forEach(step => {
            if (step.element === 'body') {
                results.valid.push(step.id);
                return;
            }
            
            try {
                const element = document.querySelector(step.element);
                if (!element) {
                    results.invalid.push(step.id);
                    return;
                }
                
                const rect = element.getBoundingClientRect();
                if (rect.width === 0 && rect.height === 0) {
                    results.hidden.push(step.id);
                    return;
                }
                
                results.valid.push(step.id);
                
            } catch (error) {
                results.invalid.push(step.id);
            }
        });
        
        return results;
    }

    /**
     * 重新加载步骤配置
     * @returns {Promise<boolean>}
     */
    async reload() {
        try {
            console.log('🔄 重新加载步骤配置...');
            
            // 清空当前步骤
            this.steps = [];
            this.customSteps.clear();
            
            // 重新初始化
            return await this.init();
            
        } catch (error) {
            console.error('❌ 重新加载步骤配置失败:', error);
            return false;
        }
    }

    /**
     * 销毁步骤配置管理器
     */
    destroy() {
        this.steps = [];
        this.customSteps.clear();
        this.isInitialized = false;
    }
}
