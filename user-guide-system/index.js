/**
 * 用户引导系统 - 主入口文件
 * 
 * 完全重构的 Driver.js 用户引导系统
 * 
 * 特性：
 * - 本地部署 Driver.js
 * - 模块化架构设计
 * - 完善的无障碍访问支持
 * - 高性能和错误处理
 * - 响应式设计
 * 
 * @version 2.0.0
 * <AUTHOR>
 */

import { GuideManager } from './core/GuideManager.js';
import { ErrorHandler } from './modules/ErrorHandler.js';
import { PerformanceMonitor } from './modules/PerformanceMonitor.js';

/**
 * 用户引导系统主类
 * 提供统一的 API 接口，保持向后兼容性
 */
class UserGuideSystem {
    constructor() {
        this.version = '2.0.0';
        this.isInitialized = false;
        this.guideManager = null;
        this.errorHandler = null;
        this.performanceMonitor = null;
        
        // 事件监听器存储
        this.eventListeners = new Map();
        
        // 配置缓存
        this.config = null;
        
        // 初始化错误处理
        this.initErrorHandler();
    }

    /**
     * 初始化错误处理器
     * @private
     */
    initErrorHandler() {
        try {
            this.errorHandler = new ErrorHandler({
                enableLogging: true,
                enableReporting: false,
                fallbackMode: true
            });
            
            // 设置全局错误处理
            this.errorHandler.setupGlobalHandlers();
        } catch (error) {
            console.error('❌ 错误处理器初始化失败:', error);
        }
    }

    /**
     * 初始化用户引导系统
     * @param {Object} config - 配置选项
     * @returns {Promise<boolean>} 初始化是否成功
     */
    async init(config = {}) {
        try {
            // 记录初始化开始时间
            const startTime = performance.now();
            
            console.log('🚀 用户引导系统 v2.0.0 初始化开始...');
            
            // 合并默认配置
            this.config = this.mergeConfig(config);
            
            // 初始化性能监控
            if (this.config.performance.enableMonitoring) {
                this.performanceMonitor = new PerformanceMonitor(this.config.performance);
                await this.performanceMonitor.init();
            }
            
            // 检查 Driver.js 是否可用
            await this.ensureDriverJS();
            
            // 初始化核心管理器
            this.guideManager = new GuideManager(this.config, this.errorHandler);
            await this.guideManager.init();
            
            // 设置事件转发
            this.setupEventForwarding();
            
            this.isInitialized = true;
            
            // 记录初始化完成时间
            const endTime = performance.now();
            const initTime = endTime - startTime;
            
            console.log(`✅ 用户引导系统初始化成功 (${initTime.toFixed(2)}ms)`);
            
            // 触发初始化完成事件
            this.emit('guide:init', { 
                version: this.version, 
                initTime,
                config: this.config 
            });
            
            return true;
            
        } catch (error) {
            this.errorHandler?.handleError(error, 'UserGuideSystem.init');
            console.error('❌ 用户引导系统初始化失败:', error);
            return false;
        }
    }

    /**
     * 合并配置选项
     * @param {Object} userConfig - 用户配置
     * @returns {Object} 合并后的配置
     * @private
     */
    mergeConfig(userConfig) {
        const defaultConfig = {
            // 基础配置
            version: '2.0.0',
            debug: false,
            autoStart: false,
            
            // 视觉配置
            theme: 'default',
            overlay: {
                color: 'rgba(0, 0, 0, 0.75)',
                blur: true,
                clickToClose: false
            },
            
            // 动画配置
            animation: {
                enabled: true,
                duration: 300,
                easing: 'ease-in-out'
            },
            
            // 无障碍配置
            accessibility: {
                enabled: true,
                minFontSize: 14,
                contrastRatio: 4.5,
                keyboardNavigation: true,
                screenReader: true,
                focusManagement: true
            },
            
            // 性能配置
            performance: {
                enableMonitoring: true,
                memoryLeakDetection: true,
                animationOptimization: true,
                maxSteps: 10
            },
            
            // 存储配置
            storage: {
                enabled: true,
                prefix: 'user_guide_v2_',
                version: '2.0.0'
            },
            
            // 错误处理配置
            errorHandling: {
                enabled: true,
                fallbackMode: true,
                retryAttempts: 3
            }
        };
        
        return this.deepMerge(defaultConfig, userConfig);
    }

    /**
     * 深度合并对象
     * @param {Object} target - 目标对象
     * @param {Object} source - 源对象
     * @returns {Object} 合并后的对象
     * @private
     */
    deepMerge(target, source) {
        const result = { ...target };
        
        for (const key in source) {
            if (source.hasOwnProperty(key)) {
                if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
                    result[key] = this.deepMerge(target[key] || {}, source[key]);
                } else {
                    result[key] = source[key];
                }
            }
        }
        
        return result;
    }

    /**
     * 确保 Driver.js 可用
     * @private
     */
    async ensureDriverJS() {
        return new Promise((resolve, reject) => {
            // 检查是否已经加载
            if (window.Driver || (window.driver && window.driver.driver)) {
                resolve();
                return;
            }
            
            // 等待 Driver.js 加载
            const checkInterval = setInterval(() => {
                if (window.Driver || (window.driver && window.driver.driver)) {
                    clearInterval(checkInterval);
                    resolve();
                }
            }, 50);
            
            // 超时处理
            setTimeout(() => {
                clearInterval(checkInterval);
                reject(new Error('Driver.js 加载超时'));
            }, 5000);
        });
    }

    /**
     * 设置事件转发
     * @private
     */
    setupEventForwarding() {
        if (!this.guideManager) return;
        
        // 转发核心事件
        const coreEvents = [
            'guide:start', 'guide:complete', 'guide:skip', 
            'guide:step:change', 'guide:error', 'guide:pause', 'guide:resume'
        ];
        
        coreEvents.forEach(eventName => {
            this.guideManager.on(eventName, (data) => {
                this.emit(eventName, data);
            });
        });
    }

    /**
     * 开始引导
     * @param {string} [stepId] - 可选的起始步骤ID
     * @returns {Promise<boolean>} 是否成功开始
     */
    async start(stepId = null) {
        if (!this.isInitialized) {
            console.warn('⚠️ 系统未初始化，正在初始化...');
            const success = await this.init();
            if (!success) {
                return false;
            }
        }
        
        try {
            return await this.guideManager.start(stepId);
        } catch (error) {
            this.errorHandler?.handleError(error, 'UserGuideSystem.start');
            return false;
        }
    }

    /**
     * 重新开始引导
     * @returns {Promise<boolean>} 是否成功重新开始
     */
    async restart() {
        try {
            if (this.guideManager) {
                return await this.guideManager.restart();
            }
            return false;
        } catch (error) {
            this.errorHandler?.handleError(error, 'UserGuideSystem.restart');
            return false;
        }
    }

    /**
     * 暂停引导
     */
    pause() {
        try {
            this.guideManager?.pause();
        } catch (error) {
            this.errorHandler?.handleError(error, 'UserGuideSystem.pause');
        }
    }

    /**
     * 恢复引导
     */
    resume() {
        try {
            this.guideManager?.resume();
        } catch (error) {
            this.errorHandler?.handleError(error, 'UserGuideSystem.resume');
        }
    }

    /**
     * 跳过引导
     */
    skip() {
        try {
            this.guideManager?.skip();
        } catch (error) {
            this.errorHandler?.handleError(error, 'UserGuideSystem.skip');
        }
    }

    /**
     * 销毁系统
     */
    destroy() {
        try {
            // 清理事件监听器
            this.eventListeners.clear();
            
            // 销毁各个模块
            this.guideManager?.destroy();
            this.performanceMonitor?.destroy();
            this.errorHandler?.destroy();
            
            // 重置状态
            this.isInitialized = false;
            this.guideManager = null;
            this.performanceMonitor = null;
            this.errorHandler = null;
            this.config = null;
            
            console.log('🗑️ 用户引导系统已销毁');
        } catch (error) {
            console.error('❌ 销毁系统时发生错误:', error);
        }
    }

    // ==================== 状态查询方法 ====================

    /**
     * 检查系统是否激活
     * @returns {boolean}
     */
    isActive() {
        return this.guideManager?.isActive() || false;
    }

    /**
     * 获取当前步骤信息
     * @returns {Object|null}
     */
    getCurrentStep() {
        return this.guideManager?.getCurrentStep() || null;
    }

    /**
     * 获取进度信息
     * @returns {Object}
     */
    getProgress() {
        return this.guideManager?.getProgress() || {
            current: 0,
            total: 0,
            percentage: 0
        };
    }

    /**
     * 检查是否已完成引导
     * @returns {boolean}
     */
    isCompleted() {
        return this.guideManager?.isCompleted() || false;
    }

    // ==================== 事件系统 ====================

    /**
     * 监听事件
     * @param {string} eventName - 事件名称
     * @param {Function} callback - 回调函数
     */
    on(eventName, callback) {
        if (!this.eventListeners.has(eventName)) {
            this.eventListeners.set(eventName, new Set());
        }
        this.eventListeners.get(eventName).add(callback);
    }

    /**
     * 移除事件监听
     * @param {string} eventName - 事件名称
     * @param {Function} callback - 回调函数
     */
    off(eventName, callback) {
        const listeners = this.eventListeners.get(eventName);
        if (listeners) {
            listeners.delete(callback);
            if (listeners.size === 0) {
                this.eventListeners.delete(eventName);
            }
        }
    }

    /**
     * 触发事件
     * @param {string} eventName - 事件名称
     * @param {*} data - 事件数据
     * @private
     */
    emit(eventName, data) {
        const listeners = this.eventListeners.get(eventName);
        if (listeners) {
            listeners.forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    this.errorHandler?.handleError(error, `Event: ${eventName}`);
                }
            });
        }
    }

    // ==================== 向后兼容性方法 ====================

    /**
     * 向后兼容：重置引导状态
     */
    reset() {
        this.guideManager?.reset();
    }

    /**
     * 向后兼容：检查是否应该显示引导
     * @returns {boolean}
     */
    shouldShowGuide() {
        return this.guideManager?.shouldShowGuide() || false;
    }

    /**
     * 向后兼容：标记为已完成
     */
    markAsCompleted() {
        this.guideManager?.markAsCompleted();
    }

    /**
     * 向后兼容：获取统计信息
     * @returns {Object}
     */
    getStats() {
        return this.guideManager?.getStats() || {};
    }
}

// 创建全局实例
const userGuideSystem = new UserGuideSystem();

// 向后兼容：保持原有的全局对象
window.UserGuide = userGuideSystem;
window.userGuide = userGuideSystem;

// 导出模块
export default userGuideSystem;
export { UserGuideSystem };
