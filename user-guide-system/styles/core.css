/**
 * 用户引导系统核心样式 v2.0
 *
 * 基于 Tailwind CSS 和 Font Awesome 的现代化样式系统
 * 集成项目设计语言，优化交互体验
 *
 * @version 2.0.0
 */

/* ===========================
   CSS自定义属性 - 与项目主题系统对齐
   ========================== */

.user-guide-system-v2 {
    /* 使用项目主题变量 */
    --guide-primary: var(--primary-color, #2196f3);
    --guide-primary-hover: var(--primary-hover, #1976d2);
    --guide-primary-light: var(--primary-light, #42a5f5);
    --guide-primary-pale: var(--primary-pale, #e3f2fd);

    --guide-surface: var(--surface-color, #ffffff);
    --guide-surface-hover: var(--surface-hover, #f5f5f5);
    --guide-surface-elevated: var(--surface-color, #ffffff);

    --guide-text-primary: var(--text-color, #212121);
    --guide-text-secondary: var(--text-secondary, #757575);
    --guide-text-tertiary: var(--text-lighter, #9e9e9e);
    --guide-text-inverse: var(--text-inverse, #ffffff);

    --guide-border: var(--border-color, #e0e0e0);
    --guide-border-light: var(--border-light, #f0f0f0);
    --guide-border-focus: var(--primary-color, #2196f3);

    --guide-shadow: var(--shadow-color, rgba(0, 0, 0, 0.1));
    --guide-shadow-hover: var(--shadow-hover, rgba(0, 0, 0, 0.15));
    --guide-shadow-focus: var(--shadow-focus, rgba(33, 150, 243, 0.25));

    --guide-success: var(--success-color, #4caf50);
    --guide-error: var(--error-color, #f44336);
    --guide-warning: var(--warning-color, #ff9800);

    /* 字体系统 - 与项目对齐 */
    --guide-font-family: var(--font-family, "PingFang SC", "Helvetica Neue", -apple-system, BlinkMacSystemFont, sans-serif);
    --guide-font-size-xs: var(--font-size-xs, 12px);
    --guide-font-size-sm: var(--font-size-sm, 14px);
    --guide-font-size-base: var(--font-size-base, 16px);
    --guide-font-size-lg: var(--font-size-lg, 18px);
    --guide-font-size-xl: var(--font-size-xl, 20px);
    --guide-font-size-2xl: var(--font-size-2xl, 24px);

    /* 间距系统 - 与项目对齐 */
    --guide-spacing-xs: var(--spacing-xs, 4px);
    --guide-spacing-sm: var(--spacing-sm, 8px);
    --guide-spacing-base: var(--spacing-base, 16px);
    --guide-spacing-lg: var(--spacing-lg, 24px);
    --guide-spacing-xl: var(--spacing-xl, 32px);

    /* 圆角系统 - 与项目对齐 */
    --guide-radius-sm: var(--radius-sm, 4px);
    --guide-radius-base: var(--radius-base, 6px);
    --guide-radius-lg: var(--radius-lg, 8px);
    --guide-radius-xl: var(--radius-xl, 12px);

    /* 动画系统 - 与项目对齐 */
    --guide-animation-duration: var(--animation-duration, 0.3s);
    --guide-animation-easing: var(--animation-easing, ease-in-out);

    /* 遮罩层配置 - 半透明深色 */
    --guide-overlay-color: rgba(0, 0, 0, 0.75);
    --guide-overlay-blur: 4px;

    /* Z-index 层级 */
    --guide-z-overlay: 999998;
    --guide-z-highlighted: 999999;
    --guide-z-popover: 1000000;
}

/* ===========================
   遮罩层样式 - 半透明深色，支持点击进入下一步
   ========================== */

.user-guide-system-v2 .driver-overlay {
    @apply fixed inset-0 w-screen h-screen transition-all duration-300 ease-in-out cursor-pointer;
    background: var(--guide-overlay-color) !important;
    backdrop-filter: blur(var(--guide-overlay-blur)) !important;
    z-index: var(--guide-z-overlay) !important;
    pointer-events: auto !important;
}

/* 遮罩层悬停效果 */
.user-guide-system-v2 .driver-overlay:hover {
    @apply cursor-pointer;
}

/* 遮罩层点击提示 */
.user-guide-system-v2 .driver-overlay::before {
    content: '';
    @apply absolute inset-0 pointer-events-none;
    background: radial-gradient(circle at center, transparent 0%, rgba(33, 150, 243, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.user-guide-system-v2 .driver-overlay:hover::before {
    opacity: 1;
}

/* 高对比度模式的遮罩 */
.user-guide-system-v2.high-contrast .driver-overlay {
    background: rgba(0, 0, 0, 0.9) !important;
    backdrop-filter: none !important;
}

/* ===========================
   高亮元素样式 - 使用 Tailwind 类名
   ========================== */

.user-guide-system-v2 .driver-highlighted-element {
    @apply relative transition-all duration-300 ease-in-out;
    z-index: var(--guide-z-highlighted) !important;
    border-radius: var(--guide-radius-lg) !important;

    /* 高亮效果 - 与项目主题色对齐 */
    box-shadow:
        0 0 0 4px var(--guide-primary),
        0 0 0 8px rgba(33, 150, 243, 0.3),
        0 0 20px rgba(33, 150, 243, 0.4) !important;
}

/* 高亮元素的脉冲动画 - 优化性能 */
@keyframes guide-highlight-pulse {
    0%, 100% {
        box-shadow:
            0 0 0 4px var(--guide-primary),
            0 0 0 8px rgba(33, 150, 243, 0.3),
            0 0 20px rgba(33, 150, 243, 0.4);
        transform: scale(1);
    }
    50% {
        box-shadow:
            0 0 0 4px var(--guide-primary),
            0 0 0 12px rgba(33, 150, 243, 0.2),
            0 0 30px rgba(33, 150, 243, 0.6);
        transform: scale(1.02);
    }
}

.user-guide-system-v2 .driver-highlighted-element {
    animation: guide-highlight-pulse 2s ease-in-out infinite !important;
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
    .user-guide-system-v2 .driver-highlighted-element {
        animation: none !important;
        transform: none !important;
    }
}

/* ===========================
   弹出框主体样式 - 使用 Tailwind 和项目设计语言
   ========================== */

.user-guide-system-v2 .driver-popover {
    @apply fixed overflow-hidden;

    /* 基础样式 - 与项目卡片样式对齐 */
    background: var(--guide-surface) !important;
    border: none !important;
    border-radius: var(--guide-radius-xl) !important;
    font-family: var(--guide-font-family) !important;

    /* 阴影效果 - 与项目 card-enhanced 对齐 */
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.15),
        0 8px 20px rgba(0, 0, 0, 0.10),
        0 0 0 1px var(--guide-border-light) !important;

    /* 尺寸控制 - 响应式设计 */
    max-width: min(420px, 90vw) !important;
    min-width: min(320px, 85vw) !important;
    width: auto !important;

    /* 层级和定位 */
    z-index: var(--guide-z-popover) !important;

    /* 动画效果 */
    animation: guide-popover-enter var(--guide-animation-duration) var(--guide-animation-easing) !important;
    transform-origin: center !important;
}

/* 弹出框进入动画 - 优化性能 */
@keyframes guide-popover-enter {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(8px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* 弹出框退出动画 */
@keyframes guide-popover-exit {
    from {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    to {
        opacity: 0;
        transform: scale(0.95) translateY(-8px);
    }
}

/* ===========================
   弹出框标题样式 - 集成 Font Awesome 图标
   ========================== */

.user-guide-system-v2 .driver-popover-title {
    @apply text-xl font-bold leading-tight m-0 flex items-center gap-3;

    /* 字体样式 - 与项目对齐 */
    color: var(--guide-text-primary) !important;
    font-size: var(--guide-font-size-xl) !important;
    line-height: 1.3 !important;

    /* 间距 - 使用 Tailwind 间距 */
    padding: 1.5rem 1.5rem 0.5rem 1.5rem !important;

    /* 渐变文字效果 - 与项目主题对齐 */
    background: linear-gradient(135deg, var(--guide-primary), var(--guide-primary-light)) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

/* 标题中的 Font Awesome 图标 */
.user-guide-system-v2 .driver-popover-title .fas,
.user-guide-system-v2 .driver-popover-title .far,
.user-guide-system-v2 .driver-popover-title .fab {
    @apply text-2xl flex-shrink-0;
    color: var(--guide-primary) !important;
    background: none !important;
    -webkit-text-fill-color: var(--guide-primary) !important;
}

/* 标题文字容器 */
.user-guide-system-v2 .driver-popover-title .title-text {
    @apply flex-1;
}

/* ===========================
   弹出框描述样式 - 优化排版
   ========================== */

.user-guide-system-v2 .driver-popover-description {
    @apply text-base font-normal leading-relaxed m-0;

    /* 字体样式 - 与项目对齐 */
    color: var(--guide-text-secondary) !important;
    font-size: var(--guide-font-size-base) !important;
    line-height: 1.6 !important;

    /* 间距 - 使用 Tailwind 间距 */
    padding: 0 1.5rem 1rem 1.5rem !important;
}

/* 强调文字样式 - 与项目 badge 样式对齐 */
.user-guide-system-v2 .driver-popover-description strong {
    @apply font-bold px-1 py-0.5 rounded;
    color: var(--guide-primary) !important;
    background: var(--guide-primary-pale) !important;
}

/* 描述中的图标 */
.user-guide-system-v2 .driver-popover-description .fas,
.user-guide-system-v2 .driver-popover-description .far,
.user-guide-system-v2 .driver-popover-description .fab {
    @apply inline-block mr-1;
    color: var(--guide-primary) !important;
}

/* 键盘提示样式 */
.user-guide-system-v2 .driver-popover-description .keyboard-hint {
    @apply text-xs text-gray-500 mt-2 p-2 bg-gray-50 rounded border-l-4 border-blue-400;
}

.user-guide-system-v2 .driver-popover-description .keyboard-hint .fas {
    @apply text-blue-500;
}

/* ===========================
   弹出框底部样式 - 添加跳过按钮区域
   ========================== */

.user-guide-system-v2 .driver-popover-footer {
    @apply flex justify-between items-center gap-2 flex-wrap relative;

    /* 样式 - 与项目卡片底部对齐 */
    padding: 0.5rem 1.5rem 1.5rem 1.5rem !important;
    border-top: 1px solid var(--guide-border-light) !important;
    background: var(--guide-surface) !important;
    border-radius: 0 0 var(--guide-radius-xl) var(--guide-radius-xl) !important;
}

/* 跳过按钮 - 位于右上角 */
.user-guide-system-v2 .driver-popover-close-btn {
    @apply absolute top-3 right-3 w-8 h-8 flex items-center justify-center rounded-full transition-all duration-200;
    background: rgba(0, 0, 0, 0.05) !important;
    border: none !important;
    color: var(--guide-text-secondary) !important;
    font-size: 14px !important;
    cursor: pointer !important;
    z-index: 10 !important;
}

.user-guide-system-v2 .driver-popover-close-btn:hover {
    @apply bg-red-50;
    color: var(--guide-error) !important;
    background: rgba(244, 67, 54, 0.1) !important;
    transform: scale(1.1);
}

.user-guide-system-v2 .driver-popover-close-btn .fas {
    @apply text-sm;
}

/* 进度指示器 - 与项目 badge 样式对齐 */
.user-guide-system-v2 .driver-popover-progress-text {
    @apply text-sm font-semibold px-3 py-1.5 rounded-lg border flex items-center gap-2;
    color: var(--guide-primary) !important;
    background: var(--guide-primary-pale) !important;
    border-color: var(--guide-primary-light) !important;
}

.user-guide-system-v2 .driver-popover-progress-text .fas {
    @apply text-xs;
    color: var(--guide-primary) !important;
}

/* 按钮组容器 */
.user-guide-system-v2 .driver-popover-footer .button-group {
    @apply flex items-center gap-2 flex-1 justify-end;
}

/* ===========================
   按钮样式系统 - 与项目按钮样式对齐
   ========================== */

.user-guide-system-v2 .driver-popover-footer button {
    @apply font-semibold leading-tight cursor-pointer transition-all duration-300 ease-in-out whitespace-nowrap overflow-hidden flex items-center gap-2;

    /* 基础样式 - 与项目对齐 */
    font-family: var(--guide-font-family) !important;
    font-size: var(--guide-font-size-sm) !important;
    line-height: 1.4 !important;

    /* 尺寸 - 与项目按钮对齐 */
    padding: 10px 20px !important;
    min-width: 80px !important;
    height: 40px !important;

    /* 边框和圆角 - 与项目对齐 */
    border: 2px solid transparent !important;
    border-radius: var(--guide-radius-base) !important;

    /* 文字处理 */
    text-overflow: ellipsis !important;
}

/* 按钮中的图标 */
.user-guide-system-v2 .driver-popover-footer button .fas,
.user-guide-system-v2 .driver-popover-footer button .far,
.user-guide-system-v2 .driver-popover-footer button .fab {
    @apply text-sm flex-shrink-0;
}

/* 按钮禁用状态 */
.user-guide-system-v2 .driver-popover-footer button:disabled {
    @apply opacity-50 cursor-not-allowed;
    transform: none !important;
}

.user-guide-system-v2 .driver-popover-footer button:disabled:hover {
    transform: none !important;
}

/* 主要按钮（下一步/完成） - 与项目主按钮样式对齐 */
.user-guide-system-v2 .driver-popover-next-btn,
.user-guide-system-v2 .driver-popover-done-btn {
    @apply text-white shadow-lg;
    background: linear-gradient(135deg, var(--guide-primary), var(--guide-primary-hover)) !important;
    border-color: var(--guide-primary) !important;
    box-shadow:
        0 4px 12px var(--guide-shadow),
        0 2px 4px rgba(33, 150, 243, 0.3) !important;
}

.user-guide-system-v2 .driver-popover-next-btn:hover,
.user-guide-system-v2 .driver-popover-done-btn:hover {
    @apply shadow-xl;
    background: linear-gradient(135deg, var(--guide-primary-hover), var(--guide-primary)) !important;
    transform: translateY(-2px) !important;
    box-shadow:
        0 8px 20px var(--guide-shadow-hover),
        0 4px 8px rgba(33, 150, 243, 0.4) !important;
}

.user-guide-system-v2 .driver-popover-next-btn:focus,
.user-guide-system-v2 .driver-popover-done-btn:focus {
    @apply outline-none;
    box-shadow:
        0 4px 12px var(--guide-shadow),
        0 0 0 3px var(--guide-shadow-focus) !important;
}

.user-guide-system-v2 .driver-popover-next-btn:active,
.user-guide-system-v2 .driver-popover-done-btn:active {
    transform: translateY(0) !important;
}

/* 次要按钮（上一步） - 与项目次要按钮样式对齐 */
.user-guide-system-v2 .driver-popover-prev-btn {
    @apply bg-white border-2;
    color: var(--guide-primary) !important;
    border-color: var(--guide-primary) !important;
    background: var(--guide-surface) !important;
}

.user-guide-system-v2 .driver-popover-prev-btn:hover {
    @apply shadow-md;
    background: var(--guide-primary-pale) !important;
    border-color: var(--guide-primary-hover) !important;
    color: var(--guide-primary-hover) !important;
    transform: translateY(-1px) !important;
}

.user-guide-system-v2 .driver-popover-prev-btn:focus {
    @apply outline-none;
    box-shadow: 0 0 0 3px var(--guide-shadow-focus) !important;
}

/* 跳过引导按钮 - 特殊样式 */
.user-guide-system-v2 .driver-popover-skip-btn {
    @apply bg-gray-50 text-gray-600 border-gray-300 hover:bg-gray-100 hover:text-gray-700;
    border: 1px solid var(--guide-border) !important;
}

.user-guide-system-v2 .driver-popover-skip-btn:hover {
    transform: translateY(-1px) !important;
}

.user-guide-system-v2 .driver-popover-skip-btn:focus {
    @apply outline-none;
    box-shadow: 0 0 0 3px rgba(156, 163, 175, 0.3) !important;
}

/* ===========================
   响应式设计 - 使用 Tailwind 断点
   ========================== */

/* 平板设备 - md 断点以下 */
@media (max-width: 768px) {
    .user-guide-system-v2 .driver-popover {
        @apply max-w-[95vw] min-w-[90vw];
    }

    .user-guide-system-v2 .driver-popover-title {
        @apply text-lg px-4 pt-4 pb-2;
        font-size: var(--guide-font-size-lg) !important;
    }

    .user-guide-system-v2 .driver-popover-description {
        @apply text-sm px-4 pb-2;
        font-size: var(--guide-font-size-sm) !important;
    }

    .user-guide-system-v2 .driver-popover-footer {
        @apply flex-col gap-1 px-4 pb-4;
    }

    .user-guide-system-v2 .driver-popover-footer button {
        @apply w-full max-w-none;
    }

    .user-guide-system-v2 .driver-popover-footer .button-group {
        @apply w-full flex-col gap-2;
    }

    /* 跳过按钮在移动端的位置调整 */
    .user-guide-system-v2 .driver-popover-close-btn {
        @apply top-2 right-2 w-7 h-7;
    }
}

/* 手机设备 - sm 断点以下 */
@media (max-width: 480px) {
    .user-guide-system-v2 .driver-popover-title {
        @apply text-base;
        font-size: var(--guide-font-size-base) !important;
    }

    .user-guide-system-v2 .driver-popover-description {
        @apply text-sm;
        font-size: var(--guide-font-size-sm) !important;
    }

    .user-guide-system-v2 .driver-popover-footer button {
        @apply text-xs px-4 py-2 h-9;
        font-size: var(--guide-font-size-xs) !important;
    }

    .user-guide-system-v2 .driver-popover-progress-text {
        @apply text-xs px-2 py-1;
    }
}

/* ===========================
   无障碍访问增强 - 符合 WCAG 标准
   ========================== */

/* 焦点样式 - 高对比度和清晰可见 */
.user-guide-system-v2 *:focus {
    @apply outline-none;
    outline: 3px solid var(--guide-border-focus) !important;
    outline-offset: 2px !important;
    box-shadow: 0 0 0 1px var(--guide-surface), 0 0 0 4px var(--guide-border-focus) !important;
}

/* 键盘导航指示器 */
.user-guide-system-v2 .keyboard-navigation-active *:focus {
    @apply ring-4 ring-blue-500 ring-opacity-50;
}

/* 屏幕阅读器专用内容 */
.user-guide-system-v2 .sr-only {
    @apply sr-only;
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .user-guide-system-v2 {
        --guide-overlay-color: rgba(0, 0, 0, 0.95);
        --guide-text-primary: #000000;
        --guide-text-secondary: #000000;
        --guide-border: #000000;
    }

    .user-guide-system-v2 .driver-popover {
        @apply border-4 border-black;
    }

    .user-guide-system-v2 .driver-highlighted-element {
        box-shadow: 0 0 0 4px #000000, 0 0 0 8px #ffffff !important;
    }

    .user-guide-system-v2 .driver-popover-close-btn {
        @apply border-2 border-black bg-white;
    }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
    .user-guide-system-v2 * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .user-guide-system-v2 .driver-overlay::before {
        transition: none !important;
    }
}

/* 大字体偏好 */
@media (prefers-font-size: large) {
    .user-guide-system-v2 .driver-popover-title {
        @apply text-2xl;
    }

    .user-guide-system-v2 .driver-popover-description {
        @apply text-lg;
    }

    .user-guide-system-v2 .driver-popover-footer button {
        @apply text-base px-6 py-3 h-12;
    }
}

/* ===========================
   特殊步骤样式 - 集成 Font Awesome 图标
   ========================== */

/* 欢迎步骤 */
.user-guide-system-v2 .guide-step-welcome .driver-popover {
    @apply text-center max-w-md;
}

.user-guide-system-v2 .guide-step-welcome .driver-popover-title {
    @apply text-2xl;
    font-size: var(--guide-font-size-2xl) !important;
    background: linear-gradient(135deg, var(--guide-primary), #9c27b0) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
}

.user-guide-system-v2 .guide-step-welcome .driver-popover-title .fas {
    @apply text-3xl;
    background: linear-gradient(135deg, var(--guide-primary), #9c27b0) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
}

/* 完成步骤 */
.user-guide-system-v2 .guide-step-complete .driver-popover {
    @apply text-center max-w-sm;
}

.user-guide-system-v2 .guide-step-complete .driver-popover-title {
    color: var(--guide-success) !important;
    background: linear-gradient(135deg, var(--guide-success), #66bb6a) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
}

.user-guide-system-v2 .guide-step-complete .driver-popover-title .fas {
    color: var(--guide-success) !important;
    -webkit-text-fill-color: var(--guide-success) !important;
}

/* AI助手步骤 */
.user-guide-system-v2 .guide-step-ai .driver-popover-title {
    background: linear-gradient(135deg, var(--guide-primary), #673ab7) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
}

.user-guide-system-v2 .guide-step-ai .driver-popover-title .fas {
    background: linear-gradient(135deg, var(--guide-primary), #673ab7) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
}

/* 功能介绍步骤 */
.user-guide-system-v2 .guide-step-feature .driver-popover-title .fas {
    @apply text-blue-500;
}

/* 导航步骤 */
.user-guide-system-v2 .guide-step-navigation .driver-popover-title .fas {
    @apply text-green-500;
}

/* 交互步骤 */
.user-guide-system-v2 .guide-step-interaction .driver-popover-title .fas {
    @apply text-purple-500;
}

/* ===========================
   工具类 - 使用 Tailwind 工具类
   ========================== */

/* 隐藏元素 */
.user-guide-hidden {
    @apply hidden;
}

/* 引导激活时的body样式 */
.user-guide-active {
    @apply overflow-hidden;
}

/* 点击提示动画 */
.user-guide-click-hint {
    @apply absolute pointer-events-none;
    animation: click-hint-pulse 2s ease-in-out infinite;
}

@keyframes click-hint-pulse {
    0%, 100% {
        @apply opacity-70 scale-100;
    }
    50% {
        @apply opacity-30 scale-110;
    }
}

/* 调试模式样式 */
.user-guide-debug {
    @apply fixed bottom-5 left-5 bg-black bg-opacity-90 text-white p-3 rounded text-xs font-mono max-w-xs;
    z-index: calc(var(--guide-z-popover) + 1) !important;
}

/* 加载状态 */
.user-guide-loading {
    @apply animate-pulse;
}

/* 错误状态 */
.user-guide-error {
    @apply border-red-500 bg-red-50 text-red-700;
}

/* 成功状态 */
.user-guide-success {
    @apply border-green-500 bg-green-50 text-green-700;
}

/* 遮罩层点击提示 */
.user-guide-overlay-hint {
    @apply absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none;
    @apply bg-white bg-opacity-90 text-gray-800 px-4 py-2 rounded-lg shadow-lg;
    @apply text-sm font-medium flex items-center gap-2;
    animation: overlay-hint-fade 3s ease-in-out infinite;
}

@keyframes overlay-hint-fade {
    0%, 100% {
        @apply opacity-0;
    }
    20%, 80% {
        @apply opacity-100;
    }
}

.user-guide-overlay-hint .fas {
    @apply text-blue-500;
}

/* 深色主题支持 */
.dark .user-guide-system-v2 {
    --guide-surface: #1f2937;
    --guide-text-primary: #f9fafb;
    --guide-text-secondary: #d1d5db;
    --guide-border: #374151;
    --guide-border-light: #4b5563;
}

.dark .user-guide-system-v2 .driver-popover {
    @apply bg-gray-800 border-gray-600;
}

.dark .user-guide-system-v2 .driver-popover-footer {
    @apply border-gray-600;
}

/* 高性能模式 */
.user-guide-performance-mode .user-guide-system-v2 * {
    @apply transition-none;
    animation: none !important;
    transform: none !important;
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .user-guide-system-v2 .driver-popover-footer button {
        @apply min-h-12 text-base;
    }

    .user-guide-system-v2 .driver-popover-close-btn {
        @apply w-10 h-10;
    }
}
