/**
 * 用户引导系统 v2.0 测试脚本
 * 
 * 验证新功能：
 * 1. Tailwind CSS 样式集成
 * 2. Font Awesome 图标系统
 * 3. 点击遮罩层进入下一步
 * 4. 跳过按钮功能
 * 5. 无障碍访问功能
 * 6. 响应式设计
 * 
 * @version 2.0.0
 */

(function() {
    'use strict';
    
    console.log('🧪 开始用户引导系统 v2.0 功能测试...');
    
    // 测试配置
    const testConfig = {
        enableLogs: true,
        testTimeout: 30000, // 30秒超时
        testSteps: [
            'initialization',
            'styling',
            'icons',
            'overlayClick',
            'skipButton',
            'accessibility',
            'responsive',
            'performance'
        ]
    };
    
    // 测试结果
    const testResults = {
        passed: 0,
        failed: 0,
        total: 0,
        details: []
    };
    
    /**
     * 日志输出
     */
    function log(message, type = 'info') {
        if (!testConfig.enableLogs) return;
        
        const prefix = {
            info: 'ℹ️',
            success: '✅',
            error: '❌',
            warning: '⚠️'
        };
        
        console.log(`${prefix[type]} ${message}`);
    }
    
    /**
     * 记录测试结果
     */
    function recordTest(testName, passed, message) {
        testResults.total++;
        if (passed) {
            testResults.passed++;
            log(`测试通过: ${testName} - ${message}`, 'success');
        } else {
            testResults.failed++;
            log(`测试失败: ${testName} - ${message}`, 'error');
        }
        
        testResults.details.push({
            name: testName,
            passed,
            message,
            timestamp: new Date().toISOString()
        });
    }
    
    /**
     * 等待元素出现
     */
    function waitForElement(selector, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const element = document.querySelector(selector);
            if (element) {
                resolve(element);
                return;
            }
            
            const observer = new MutationObserver((mutations) => {
                const element = document.querySelector(selector);
                if (element) {
                    observer.disconnect();
                    resolve(element);
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
            
            setTimeout(() => {
                observer.disconnect();
                reject(new Error(`元素 ${selector} 在 ${timeout}ms 内未出现`));
            }, timeout);
        });
    }
    
    /**
     * 测试系统初始化
     */
    async function testInitialization() {
        log('测试系统初始化...');
        
        try {
            // 检查全局对象
            if (!window.UserGuide) {
                throw new Error('window.UserGuide 不存在');
            }
            
            // 检查必要方法
            const requiredMethods = ['init', 'start', 'restart', 'skip', 'destroy'];
            for (const method of requiredMethods) {
                if (typeof window.UserGuide[method] !== 'function') {
                    throw new Error(`方法 ${method} 不存在或不是函数`);
                }
            }
            
            // 检查配置
            if (!window.UserGuide.config) {
                throw new Error('配置对象不存在');
            }
            
            recordTest('系统初始化', true, '所有必要组件已正确加载');
            
        } catch (error) {
            recordTest('系统初始化', false, error.message);
        }
    }
    
    /**
     * 测试样式集成
     */
    async function testStyling() {
        log('测试 Tailwind CSS 样式集成...');
        
        try {
            // 启动引导以创建弹出框
            await window.UserGuide.start();
            
            // 等待弹出框出现
            const popover = await waitForElement('.driver-popover');
            
            // 检查 Tailwind 类名
            const titleElement = popover.querySelector('.driver-popover-title');
            if (!titleElement) {
                throw new Error('标题元素不存在');
            }
            
            // 检查是否应用了 Tailwind 样式
            const computedStyle = window.getComputedStyle(titleElement);
            const hasFlexDisplay = computedStyle.display === 'flex';
            const hasGap = computedStyle.gap !== 'normal' && computedStyle.gap !== '0px';
            
            if (!hasFlexDisplay) {
                throw new Error('标题元素未应用 flex 布局');
            }
            
            // 检查自定义 CSS 变量
            const rootStyle = window.getComputedStyle(document.documentElement);
            const primaryColor = rootStyle.getPropertyValue('--guide-primary');
            
            if (!primaryColor) {
                log('CSS 变量可能未正确设置', 'warning');
            }
            
            recordTest('样式集成', true, 'Tailwind CSS 样式已正确应用');
            
        } catch (error) {
            recordTest('样式集成', false, error.message);
        }
    }
    
    /**
     * 测试 Font Awesome 图标
     */
    async function testIcons() {
        log('测试 Font Awesome 图标集成...');
        
        try {
            const popover = document.querySelector('.driver-popover');
            if (!popover) {
                throw new Error('弹出框不存在');
            }
            
            // 检查标题中的图标
            const titleIcon = popover.querySelector('.driver-popover-title .fas');
            if (!titleIcon) {
                throw new Error('标题中未找到 Font Awesome 图标');
            }
            
            // 检查按钮中的图标
            const buttons = popover.querySelectorAll('button .fas');
            if (buttons.length === 0) {
                throw new Error('按钮中未找到 Font Awesome 图标');
            }
            
            // 检查图标是否正确渲染
            const iconStyle = window.getComputedStyle(titleIcon);
            const fontFamily = iconStyle.fontFamily;
            
            if (!fontFamily.includes('Font Awesome')) {
                log('Font Awesome 字体可能未正确加载', 'warning');
            }
            
            recordTest('图标集成', true, `找到 ${buttons.length + 1} 个 Font Awesome 图标`);
            
        } catch (error) {
            recordTest('图标集成', false, error.message);
        }
    }
    
    /**
     * 测试遮罩层点击功能
     */
    async function testOverlayClick() {
        log('测试遮罩层点击功能...');
        
        try {
            const overlay = document.querySelector('.driver-overlay');
            if (!overlay) {
                throw new Error('遮罩层不存在');
            }
            
            // 检查遮罩层是否有点击样式
            const overlayStyle = window.getComputedStyle(overlay);
            const hasCursorPointer = overlayStyle.cursor === 'pointer';
            
            if (!hasCursorPointer) {
                throw new Error('遮罩层未设置 cursor: pointer');
            }
            
            // 获取当前步骤
            const currentStep = window.UserGuide.getCurrentStep();
            const currentIndex = currentStep ? currentStep.index : 0;
            
            // 模拟点击遮罩层
            const clickEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                clientX: 100,
                clientY: 100
            });
            
            overlay.dispatchEvent(clickEvent);
            
            // 等待一段时间让动画完成
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // 检查是否进入下一步
            const newStep = window.UserGuide.getCurrentStep();
            const newIndex = newStep ? newStep.index : 0;
            
            if (newIndex <= currentIndex) {
                throw new Error('点击遮罩层后未进入下一步');
            }
            
            recordTest('遮罩层点击', true, '点击遮罩层成功进入下一步');
            
        } catch (error) {
            recordTest('遮罩层点击', false, error.message);
        }
    }
    
    /**
     * 测试跳过按钮
     */
    async function testSkipButton() {
        log('测试跳过按钮功能...');
        
        try {
            const popover = document.querySelector('.driver-popover');
            if (!popover) {
                throw new Error('弹出框不存在');
            }
            
            // 查找跳过按钮
            const skipButton = popover.querySelector('.driver-popover-close-btn');
            if (!skipButton) {
                throw new Error('跳过按钮不存在');
            }
            
            // 检查按钮位置
            const buttonStyle = window.getComputedStyle(skipButton);
            const isAbsolute = buttonStyle.position === 'absolute';
            const hasTopRight = buttonStyle.top !== 'auto' && buttonStyle.right !== 'auto';
            
            if (!isAbsolute || !hasTopRight) {
                throw new Error('跳过按钮位置不正确');
            }
            
            // 检查按钮图标
            const icon = skipButton.querySelector('.fas');
            if (!icon) {
                throw new Error('跳过按钮缺少图标');
            }
            
            // 检查 ARIA 属性
            const ariaLabel = skipButton.getAttribute('aria-label');
            if (!ariaLabel) {
                throw new Error('跳过按钮缺少 aria-label');
            }
            
            recordTest('跳过按钮', true, '跳过按钮样式和功能正确');
            
        } catch (error) {
            recordTest('跳过按钮', false, error.message);
        }
    }
    
    /**
     * 测试无障碍访问
     */
    async function testAccessibility() {
        log('测试无障碍访问功能...');
        
        try {
            const popover = document.querySelector('.driver-popover');
            if (!popover) {
                throw new Error('弹出框不存在');
            }
            
            // 检查 ARIA 属性
            const role = popover.getAttribute('role');
            const ariaModal = popover.getAttribute('aria-modal');
            const ariaLabelledby = popover.getAttribute('aria-labelledby');
            
            if (role !== 'dialog') {
                throw new Error('弹出框缺少正确的 role 属性');
            }
            
            if (ariaModal !== 'true') {
                throw new Error('弹出框缺少 aria-modal 属性');
            }
            
            // 检查焦点管理
            const focusableElements = popover.querySelectorAll('button, [tabindex]:not([tabindex="-1"])');
            if (focusableElements.length === 0) {
                throw new Error('弹出框中没有可聚焦元素');
            }
            
            // 检查键盘导航
            const buttons = popover.querySelectorAll('button');
            let hasTabIndex = false;
            buttons.forEach(button => {
                if (button.tabIndex >= 0) {
                    hasTabIndex = true;
                }
            });
            
            if (!hasTabIndex) {
                log('按钮可能缺少正确的 tabindex', 'warning');
            }
            
            recordTest('无障碍访问', true, '无障碍访问功能基本正确');
            
        } catch (error) {
            recordTest('无障碍访问', false, error.message);
        }
    }
    
    /**
     * 测试响应式设计
     */
    async function testResponsive() {
        log('测试响应式设计...');
        
        try {
            const popover = document.querySelector('.driver-popover');
            if (!popover) {
                throw new Error('弹出框不存在');
            }
            
            // 获取原始尺寸
            const originalWidth = window.innerWidth;
            const originalHeight = window.innerHeight;
            
            // 模拟移动设备尺寸
            Object.defineProperty(window, 'innerWidth', {
                writable: true,
                configurable: true,
                value: 375
            });
            
            Object.defineProperty(window, 'innerHeight', {
                writable: true,
                configurable: true,
                value: 667
            });
            
            // 触发 resize 事件
            window.dispatchEvent(new Event('resize'));
            
            // 等待样式更新
            await new Promise(resolve => setTimeout(resolve, 100));
            
            // 检查弹出框是否适应小屏幕
            const popoverStyle = window.getComputedStyle(popover);
            const maxWidth = popoverStyle.maxWidth;
            
            // 恢复原始尺寸
            Object.defineProperty(window, 'innerWidth', {
                writable: true,
                configurable: true,
                value: originalWidth
            });
            
            Object.defineProperty(window, 'innerHeight', {
                writable: true,
                configurable: true,
                value: originalHeight
            });
            
            window.dispatchEvent(new Event('resize'));
            
            recordTest('响应式设计', true, '响应式设计基本正确');
            
        } catch (error) {
            recordTest('响应式设计', false, error.message);
        }
    }
    
    /**
     * 测试性能
     */
    async function testPerformance() {
        log('测试性能指标...');
        
        try {
            // 检查是否有性能监控
            if (window.UserGuide.performanceMonitor) {
                const stats = window.UserGuide.performanceMonitor.getStats();
                
                if (stats.initialization && stats.initialization.duration > 2000) {
                    throw new Error(`初始化时间过长: ${stats.initialization.duration}ms`);
                }
                
                recordTest('性能测试', true, '性能指标正常');
            } else {
                recordTest('性能测试', true, '性能监控器未启用（可选）');
            }
            
        } catch (error) {
            recordTest('性能测试', false, error.message);
        }
    }
    
    /**
     * 运行所有测试
     */
    async function runAllTests() {
        log('开始运行所有测试...');
        
        try {
            // 等待系统就绪
            await new Promise((resolve) => {
                if (window.UserGuide) {
                    resolve();
                } else {
                    window.addEventListener('userGuideReady', resolve);
                }
            });
            
            // 运行测试
            await testInitialization();
            await testStyling();
            await testIcons();
            await testOverlayClick();
            await testSkipButton();
            await testAccessibility();
            await testResponsive();
            await testPerformance();
            
            // 清理
            if (window.UserGuide && window.UserGuide.isActive()) {
                window.UserGuide.skip();
            }
            
        } catch (error) {
            log(`测试运行失败: ${error.message}`, 'error');
        }
        
        // 输出测试结果
        outputTestResults();
    }
    
    /**
     * 输出测试结果
     */
    function outputTestResults() {
        console.log('\n📊 测试结果汇总:');
        console.log(`总计: ${testResults.total} 个测试`);
        console.log(`通过: ${testResults.passed} 个`);
        console.log(`失败: ${testResults.failed} 个`);
        console.log(`成功率: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
        
        if (testResults.failed > 0) {
            console.log('\n❌ 失败的测试:');
            testResults.details
                .filter(test => !test.passed)
                .forEach(test => {
                    console.log(`  - ${test.name}: ${test.message}`);
                });
        }
        
        // 在页面上显示结果
        displayResultsOnPage();
    }
    
    /**
     * 在页面上显示测试结果
     */
    function displayResultsOnPage() {
        const resultDiv = document.createElement('div');
        resultDiv.id = 'test-results';
        resultDiv.className = 'fixed top-4 left-4 bg-white border-2 border-gray-300 rounded-lg p-4 shadow-lg max-w-sm z-50';
        
        const successRate = ((testResults.passed / testResults.total) * 100).toFixed(1);
        const statusColor = testResults.failed === 0 ? 'text-green-600' : 'text-red-600';
        
        resultDiv.innerHTML = `
            <div class="flex items-center gap-2 mb-3">
                <i class="fas fa-flask text-blue-500"></i>
                <h3 class="font-bold text-gray-800">测试结果</h3>
                <button onclick="this.closest('#test-results').remove()" class="ml-auto text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                    <span>总计:</span>
                    <span class="font-medium">${testResults.total} 个测试</span>
                </div>
                <div class="flex justify-between">
                    <span>通过:</span>
                    <span class="font-medium text-green-600">${testResults.passed} 个</span>
                </div>
                <div class="flex justify-between">
                    <span>失败:</span>
                    <span class="font-medium text-red-600">${testResults.failed} 个</span>
                </div>
                <div class="flex justify-between border-t pt-2">
                    <span>成功率:</span>
                    <span class="font-bold ${statusColor}">${successRate}%</span>
                </div>
            </div>
        `;
        
        document.body.appendChild(resultDiv);
        
        // 5秒后自动隐藏
        setTimeout(() => {
            if (resultDiv.parentNode) {
                resultDiv.remove();
            }
        }, 10000);
    }
    
    // 自动运行测试
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', runAllTests);
    } else {
        setTimeout(runAllTests, 1000);
    }
    
    // 导出测试函数供手动调用
    window.testUserGuideV2 = {
        runAllTests,
        testInitialization,
        testStyling,
        testIcons,
        testOverlayClick,
        testSkipButton,
        testAccessibility,
        testResponsive,
        testPerformance,
        getResults: () => testResults
    };
    
})();
