/**
 * 增强版用户引导系统样式
 * 解决视觉问题并提升用户体验
 */

/* 基础引导样式重写 - 高对比度设计 */
.user-guide-driver-enhanced .driver-popover {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%) !important;
    color: #ffffff !important;
    border-radius: 12px !important;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5),
                0 0 0 3px rgba(255, 255, 255, 0.2),
                0 8px 32px rgba(0, 0, 0, 0.3) !important;
    min-width: 320px !important;
    max-width: 420px !important;
    padding: 24px !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    backdrop-filter: blur(10px) !important;
    border: 3px solid rgba(255, 255, 255, 0.3) !important;
    position: relative !important;
    /* 确保最高z-index */
    z-index: 999999 !important;
}

/* 标题样式优化 - 增强可读性 */
.user-guide-driver-enhanced .driver-popover-title {
    font-size: 22px !important;
    font-weight: 700 !important;
    color: #ffffff !important;
    margin-bottom: 12px !important;
    line-height: 1.3 !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
    letter-spacing: 0.5px !important;
}

/* 描述文字样式优化 - 高对比度 */
.user-guide-driver-enhanced .driver-popover-description {
    font-size: 16px !important; /* 符合WCAG最小字体要求 */
    line-height: 1.7 !important; /* 增加行高提升可读性 */
    color: #ffffff !important; /* 使用纯白色确保最高对比度 */
    margin-bottom: 24px !important;
    font-weight: 500 !important; /* 增加字重提升可读性 */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
    /* 增加背景提升对比度 */
    background: rgba(0, 0, 0, 0.1) !important;
    padding: 12px !important;
    border-radius: 6px !important;
}

.user-guide-driver-enhanced .driver-popover-description strong {
    color: #ffd700 !important;
    font-weight: 600 !important;
}

/* 按钮样式现代化 - 高对比度设计 */
.user-guide-driver-enhanced .driver-popover-footer button {
    background: rgba(255, 255, 255, 0.25) !important;
    color: #ffffff !important;
    border: 2px solid rgba(255, 255, 255, 0.5) !important;
    border-radius: 8px !important;
    padding: 14px 28px !important; /* 增大点击区域 */
    font-size: 16px !important; /* 增大字体符合可访问性标准 */
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    backdrop-filter: blur(10px) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
    min-width: 100px !important; /* 增大最小宽度 */
    min-height: 44px !important; /* 确保符合WCAG点击区域标准 */
}

.user-guide-driver-enhanced .driver-popover-footer button:hover {
    background: rgba(255, 255, 255, 0.35) !important;
    border-color: rgba(255, 255, 255, 0.6) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3) !important;
    color: #ffffff !important;
}

.user-guide-driver-enhanced .driver-popover-footer button:active {
    transform: translateY(0) !important;
}

/* 增强焦点状态的可访问性 */
.user-guide-driver-enhanced .driver-popover-footer button:focus {
    outline: 3px solid #ffd700 !important;
    outline-offset: 2px !important;
    background: rgba(255, 255, 255, 0.4) !important;
    border-color: rgba(255, 255, 255, 0.7) !important;
}

/* 键盘导航支持 */
.user-guide-driver-enhanced .driver-popover-footer button:focus-visible {
    outline: 3px solid #ffd700 !important;
    outline-offset: 2px !important;
    box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.3) !important;
}

/* 关闭按钮样式 */
.user-guide-driver-enhanced .driver-popover-close-btn {
    color: rgba(255, 255, 255, 0.7) !important;
    font-size: 20px !important;
    width: 36px !important;
    height: 36px !important;
    border-radius: 50% !important;
    background: rgba(255, 255, 255, 0.1) !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.user-guide-driver-enhanced .driver-popover-close-btn:hover {
    color: #ffffff !important;
    background: rgba(255, 255, 255, 0.2) !important;
    transform: scale(1.1) !important;
}

/* 进度文字样式 */
.user-guide-driver-enhanced .driver-popover-progress-text {
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 13px !important;
    font-weight: 500 !important;
}

/* 箭头样式优化 */
.user-guide-driver-enhanced .driver-popover-arrow {
    border-color: #667eea !important;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2)) !important;
}

/* 遮罩层优化 - 半透明深色遮罩，提升可见性 */
.user-guide-driver-enhanced .driver-overlay {
    background: rgba(0, 0, 0, 0.7) !important;
    backdrop-filter: blur(3px) !important;
    transition: all 0.3s ease !important;
}

/* 高亮元素边框效果 */
.user-guide-driver-enhanced .driver-active-element {
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.6), 
                0 0 0 8px rgba(102, 126, 234, 0.3),
                0 0 20px rgba(102, 126, 234, 0.4) !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
}

/* 特定步骤的自定义样式 */
.guide-step-welcome .driver-popover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    text-align: center !important;
}

.guide-step-interactive .driver-popover {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
    border-left: 4px solid #ffd700 !important;
}

.guide-step-interactive .driver-popover-title::before {
    content: "🎯 ";
    font-size: 24px !important;
}

.guide-step-complete .driver-popover {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    text-align: center !important;
}

/* 交互式步骤的特殊样式 */
.interactive-step-highlight {
    animation: pulse-highlight 2s infinite !important;
}

@keyframes pulse-highlight {
    0%, 100% {
        box-shadow: 0 0 0 4px rgba(255, 215, 0, 0.6) !important;
    }
    50% {
        box-shadow: 0 0 0 8px rgba(255, 215, 0, 0.8) !important;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .user-guide-driver-enhanced .driver-popover {
        min-width: 280px !important;
        max-width: 320px !important;
        padding: 20px !important;
    }
    
    .user-guide-driver-enhanced .driver-popover-title {
        font-size: 20px !important;
    }
    
    .user-guide-driver-enhanced .driver-popover-description {
        font-size: 15px !important;
    }
}

@media (max-width: 480px) {
    .user-guide-driver-enhanced .driver-popover {
        min-width: 260px !important;
        max-width: 300px !important;
        padding: 16px !important;
    }
    
    .user-guide-driver-enhanced .driver-popover-title {
        font-size: 18px !important;
    }
    
    .user-guide-driver-enhanced .driver-popover-description {
        font-size: 14px !important;
    }
}

/* 动画效果增强 */
.user-guide-driver-enhanced .driver-popover {
    animation: slideInScale 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
}

@keyframes slideInScale {
    0% {
        opacity: 0;
        transform: scale(0.8) translateY(20px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* 菜单项交互高亮样式 */
.guide-menu-item-highlight {
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.2), rgba(102, 126, 234, 0.1)) !important;
    border-left: 4px solid #667eea !important;
    animation: menu-item-pulse 1.5s ease-in-out infinite !important;
    transition: all 0.3s ease !important;
}

@keyframes menu-item-pulse {
    0%, 100% {
        background: linear-gradient(90deg, rgba(102, 126, 234, 0.2), rgba(102, 126, 234, 0.1)) !important;
    }
    50% {
        background: linear-gradient(90deg, rgba(102, 126, 234, 0.3), rgba(102, 126, 234, 0.2)) !important;
    }
}

/* 页面切换时的过渡效果 */
.guide-page-transition {
    transition: opacity 0.3s ease-in-out !important;
}

.guide-page-transition.fade-out {
    opacity: 0.3 !important;
}

.guide-page-transition.fade-in {
    opacity: 1 !important;
}

/* 确保在所有主题下的可见性 */
.user-guide-driver-enhanced .driver-popover * {
    color: inherit !important;
}

/* 强制确保文字可见性 */
.user-guide-driver-enhanced .driver-popover-title,
.user-guide-driver-enhanced .driver-popover-description,
.user-guide-driver-enhanced .driver-popover-footer button {
    text-rendering: optimizeLegibility !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
}

/* 增强遮罩层的可访问性 */
.user-guide-driver-enhanced .driver-overlay {
    z-index: 9998 !important;
}

.user-guide-driver-enhanced .driver-popover {
    z-index: 9999 !important;
}

/* 确保高亮元素的可见性 */
.user-guide-driver-enhanced .driver-active-element {
    z-index: 9997 !important;
    position: relative !important;
}
