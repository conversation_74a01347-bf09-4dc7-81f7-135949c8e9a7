<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户引导系统 v2.0 演示</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdn.staticfile.net/font-awesome/6.5.1/css/all.min.css">
    
    <!-- Driver.js CSS -->
    <link rel="stylesheet" href="libs/driver.js/driver.min.css">
    
    <!-- 用户引导系统 v2.0 样式 -->
    <link rel="stylesheet" href="user-guide-system/styles/core.css">
    
    <style>
        /* 演示页面样式 */
        .demo-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .demo-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .demo-sidebar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }
        
        .demo-main {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body class="demo-container">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <div class="sidebar demo-sidebar w-64 p-6 shadow-lg">
            <div class="mb-8">
                <h1 class="text-2xl font-bold text-gray-800 flex items-center gap-2">
                    <i class="fas fa-rocket text-blue-500"></i>
                    跨境运营助手
                </h1>
                <p class="text-sm text-gray-600 mt-1">v2.0 演示版本</p>
            </div>
            
            <nav class="space-y-2">
                <div class="menu-item bg-blue-50 text-blue-700 px-4 py-3 rounded-lg flex items-center gap-3 cursor-pointer hover:bg-blue-100 transition-colors">
                    <i class="fas fa-chart-bar"></i>
                    <span>仪表盘</span>
                </div>
                
                <div class="menu-item text-gray-700 px-4 py-3 rounded-lg flex items-center gap-3 cursor-pointer hover:bg-gray-100 transition-colors">
                    <i class="fas fa-box"></i>
                    <span>产品库</span>
                </div>
                
                <div class="menu-item text-gray-700 px-4 py-3 rounded-lg flex items-center gap-3 cursor-pointer hover:bg-gray-100 transition-colors">
                    <i class="fas fa-users"></i>
                    <span>建联记录</span>
                </div>
                
                <div id="ai-assistant-menu" class="menu-item text-gray-700 px-4 py-3 rounded-lg flex items-center gap-3 cursor-pointer hover:bg-gray-100 transition-colors">
                    <i class="fas fa-robot"></i>
                    <span>AI助手</span>
                    <span class="ml-auto bg-green-500 text-white text-xs px-2 py-1 rounded-full">NEW</span>
                </div>
            </nav>
            
            <div class="mt-8">
                <div class="new-chat bg-gradient-to-r from-blue-500 to-purple-600 text-white px-4 py-3 rounded-lg flex items-center gap-3 cursor-pointer hover:from-blue-600 hover:to-purple-700 transition-all">
                    <i class="fas fa-plus"></i>
                    <span>新建商品分析</span>
                </div>
            </div>
            
            <div class="mt-auto pt-8">
                <div id="user-profile-sidebar" class="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-100 cursor-pointer">
                    <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold">
                        U
                    </div>
                    <div class="flex-1">
                        <div class="font-medium text-gray-800">演示用户</div>
                        <div class="text-sm text-gray-600"><EMAIL></div>
                    </div>
                    <i class="fas fa-chevron-down text-gray-400"></i>
                </div>
            </div>
        </div>
        
        <!-- 主内容区 -->
        <div class="flex-1 flex flex-col">
            <!-- 顶部栏 -->
            <header class="demo-main p-6 shadow-sm border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-800">AI助手</h2>
                        <p class="text-gray-600">智能分析商品，推荐合适的合作博主</p>
                    </div>
                    
                    <div class="flex items-center gap-4">
                        <div class="notification-container relative">
                            <button class="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors">
                                <i class="fas fa-bell"></i>
                                <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-5 h-5 rounded-full flex items-center justify-center">3</span>
                            </button>
                        </div>
                        
                        <button class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center gap-2">
                            <i class="fas fa-question-circle"></i>
                            开始引导
                        </button>
                    </div>
                </div>
            </header>
            
            <!-- 主工作区 -->
            <main class="main-content flex-1 p-6">
                <div class="central-input-container max-w-2xl mx-auto">
                    <div class="demo-card rounded-2xl p-8 text-center">
                        <div class="mb-6">
                            <i class="fas fa-robot text-6xl text-blue-500 mb-4"></i>
                            <h3 class="text-2xl font-bold text-white mb-2">AI商品分析助手</h3>
                            <p class="text-white/80">粘贴商品链接，获取智能分析和博主推荐</p>
                        </div>
                        
                        <div class="space-y-4">
                            <div class="relative">
                                <input 
                                    type="text" 
                                    placeholder="请粘贴商品链接（支持亚马逊、eBay、速卖通等）"
                                    class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all"
                                >
                                <button class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-500 text-white px-4 py-1.5 rounded-md hover:bg-blue-600 transition-colors">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                            
                            <div class="flex gap-2 justify-center">
                                <button class="quick-prompt-btn px-4 py-2 bg-white/20 text-white rounded-lg hover:bg-white/30 transition-colors text-sm">
                                    <i class="fas fa-play"></i>
                                    试用演示
                                </button>
                                <button class="quick-prompt-btn px-4 py-2 bg-white/20 text-white rounded-lg hover:bg-white/30 transition-colors text-sm">
                                    <i class="fas fa-upload"></i>
                                    上传图片
                                </button>
                                <button class="quick-prompt-btn px-4 py-2 bg-white/20 text-white rounded-lg hover:bg-white/30 transition-colors text-sm">
                                    <i class="fas fa-history"></i>
                                    历史记录
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="demo-card rounded-xl p-6 text-center">
                            <i class="fas fa-search text-3xl text-blue-400 mb-3"></i>
                            <h4 class="font-bold text-white mb-2">智能分析</h4>
                            <p class="text-white/70 text-sm">自动解析商品特点和卖点</p>
                        </div>
                        
                        <div class="demo-card rounded-xl p-6 text-center">
                            <i class="fas fa-user-friends text-3xl text-green-400 mb-3"></i>
                            <h4 class="font-bold text-white mb-2">博主推荐</h4>
                            <p class="text-white/70 text-sm">匹配最适合的合作博主</p>
                        </div>
                        
                        <div class="demo-card rounded-xl p-6 text-center">
                            <i class="fas fa-envelope text-3xl text-purple-400 mb-3"></i>
                            <h4 class="font-bold text-white mb-2">邮件生成</h4>
                            <p class="text-white/70 text-sm">个性化沟通邮件模板</p>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <!-- 浮动操作按钮 -->
    <div class="fixed bottom-6 right-6 space-y-3">
        <button id="start-guide-btn" class="w-14 h-14 bg-blue-500 text-white rounded-full shadow-lg hover:bg-blue-600 transition-all hover:scale-110 flex items-center justify-center">
            <i class="fas fa-question-circle text-xl"></i>
        </button>
        
        <button id="restart-guide-btn" class="w-12 h-12 bg-green-500 text-white rounded-full shadow-lg hover:bg-green-600 transition-all hover:scale-110 flex items-center justify-center">
            <i class="fas fa-redo text-sm"></i>
        </button>
        
        <button id="skip-guide-btn" class="w-12 h-12 bg-red-500 text-white rounded-full shadow-lg hover:bg-red-600 transition-all hover:scale-110 flex items-center justify-center">
            <i class="fas fa-times text-sm"></i>
        </button>

        <button id="test-guide-btn" class="w-12 h-12 bg-purple-500 text-white rounded-full shadow-lg hover:bg-purple-600 transition-all hover:scale-110 flex items-center justify-center">
            <i class="fas fa-flask text-sm"></i>
        </button>
    </div>
    
    <!-- Driver.js -->
    <script src="libs/driver.js/driver.min.js"></script>
    
    <!-- 用户引导系统 v2.0 -->
    <script src="user-guide-init.js"></script>

    <!-- 测试脚本 -->
    <script src="test-user-guide-v2.js"></script>
    
    <script>
        // 演示页面交互
        document.addEventListener('DOMContentLoaded', function() {
            // 等待用户引导系统加载
            window.addEventListener('userGuideReady', function(event) {
                console.log('用户引导系统已就绪:', event.detail);
                
                // 绑定演示按钮事件
                document.getElementById('start-guide-btn').addEventListener('click', function() {
                    if (window.UserGuide) {
                        window.UserGuide.start();
                    }
                });
                
                document.getElementById('restart-guide-btn').addEventListener('click', function() {
                    if (window.UserGuide) {
                        window.UserGuide.restart();
                    }
                });
                
                document.getElementById('skip-guide-btn').addEventListener('click', function() {
                    if (window.UserGuide) {
                        window.UserGuide.skip();
                    }
                });

                document.getElementById('test-guide-btn').addEventListener('click', function() {
                    if (window.testUserGuideV2) {
                        window.testUserGuideV2.runAllTests();
                    } else {
                        alert('测试脚本未加载');
                    }
                });
                
                // 顶部开始引导按钮
                document.querySelector('header button').addEventListener('click', function() {
                    if (window.UserGuide) {
                        window.UserGuide.start();
                    }
                });
            });
            
            // 菜单项交互
            document.querySelectorAll('.menu-item').forEach(item => {
                item.addEventListener('click', function() {
                    // 移除其他项的激活状态
                    document.querySelectorAll('.menu-item').forEach(i => {
                        i.classList.remove('bg-blue-50', 'text-blue-700');
                        i.classList.add('text-gray-700');
                    });
                    
                    // 激活当前项
                    this.classList.add('bg-blue-50', 'text-blue-700');
                    this.classList.remove('text-gray-700');
                });
            });
            
            // 快速操作按钮
            document.querySelectorAll('.quick-prompt-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const icon = this.querySelector('i');
                    const originalClass = icon.className;
                    
                    // 添加加载动画
                    icon.className = 'fas fa-spinner fa-spin';
                    
                    setTimeout(() => {
                        icon.className = originalClass;
                        
                        // 显示模拟结果
                        if (this.textContent.includes('演示')) {
                            alert('演示功能：这里会显示商品分析结果和博主推荐！');
                        }
                    }, 1500);
                });
            });
        });
    </script>
</body>
</html>
