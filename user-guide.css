/**
 * 新手引导系统样式 - 现代化美观响应式版本
 * 
 * 🎨 严格遵循项目CSS变量系统
 * 📱 完美响应式设计 
 * ✨ 现代化视觉效果
 * 🔧 解决按钮换行问题
 */

/* ===========================
   CSS变量定义 - 符合项目规范
   ========================== */

.user-guide-driver {
    /* 🎨 使用项目主题变量系统 */
    --guide-primary: var(--primary-color, #1976d2);
    --guide-primary-hover: var(--primary-hover, #1565c0);
    --guide-primary-light: var(--primary-light, #42a5f5);
    --guide-primary-pale: var(--primary-pale, #e3f2fd);
    
    --guide-surface: var(--surface-color, #ffffff);
    --guide-surface-hover: var(--surface-hover, #f5f5f5);
    --guide-surface-elevated: var(--surface-elevated, #ffffff);
    
    --guide-text-primary: var(--text-primary, #212121);
    --guide-text-secondary: var(--text-secondary, #757575);
    --guide-text-tertiary: var(--text-tertiary, #9e9e9e);
    --guide-text-inverse: var(--text-inverse, #ffffff);
    
    --guide-border: var(--border-color, #e0e0e0);
    --guide-border-light: var(--border-light, #f0f0f0);
    --guide-border-focus: var(--border-focus, #2196f3);
    
    --guide-shadow: var(--shadow-color, rgba(0, 0, 0, 0.1));
    --guide-shadow-hover: var(--shadow-hover, rgba(0, 0, 0, 0.15));
    --guide-shadow-focus: var(--shadow-focus, rgba(33, 150, 243, 0.25));
    
    --guide-success: var(--success-color, #4caf50);
    --guide-error: var(--error-color, #f44336);
    --guide-warning: var(--warning-color, #ff9800);
    
    /* 响应式字体变量 */
    --guide-font-family: var(--font-family, "PingFang SC", "Helvetica Neue", -apple-system, BlinkMacSystemFont, sans-serif);
    --guide-font-size-title: var(--font-size-xl, 20px);
    --guide-font-size-body: var(--font-size-base, 16px);
    --guide-font-size-small: var(--font-size-sm, 14px);
    
    /* 间距变量 */
    --guide-spacing-xs: var(--spacing-xs, 4px);
    --guide-spacing-sm: var(--spacing-sm, 8px);
    --guide-spacing-base: var(--spacing-base, 16px);
    --guide-spacing-lg: var(--spacing-lg, 24px);
    
    /* 圆角变量 */
    --guide-radius-sm: var(--radius-sm, 4px);
    --guide-radius-base: var(--radius-base, 6px);
    --guide-radius-lg: var(--radius-lg, 8px);
    --guide-radius-xl: var(--radius-xl, 12px);
    
    /* 动画变量 */
    --guide-animation-duration: var(--animation-duration, 0.3s);
    --guide-animation-easing: var(--animation-easing, ease-in-out);
}

/* ===========================
   遮罩层样式优化
   ========================== */

/* 半透明深色遮罩 - 替代全黑遮罩 */
.user-guide-driver .driver-overlay,
.user-guide-driver-enhanced .driver-overlay {
    background-color: rgba(0, 0, 0, 0.75) !important; /* 半透明深色遮罩 */
    backdrop-filter: blur(4px) !important; /* 添加轻微模糊效果 */
    transition: all 0.3s ease !important;
}

/* 高对比度主题的遮罩 */
.user-guide-driver.high-contrast .driver-overlay,
.user-guide-driver-enhanced.high-contrast .driver-overlay {
    background-color: rgba(0, 0, 0, 0.85) !important; /* 更深的遮罩以提高对比度 */
}

/* 确保遮罩层在正确的层级 */
.driver-overlay {
    z-index: 999999 !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    pointer-events: auto !important;
}

/* 遮罩层样式 - 温和修复 */
.driver-overlay {
    background: rgba(0, 0, 0, 0.85) !important;
    backdrop-filter: blur(1px) !important;
    transition: all 0.3s ease-in-out !important;
    z-index: 999998 !important; /* 比弹出框低一层 */
}

/* 高对比度主题的遮罩 */
.user-guide-driver.high-contrast .driver-overlay,
.user-guide-driver-enhanced.high-contrast .driver-overlay {
    background: rgba(0, 0, 0, 0.95) !important; /* 更深的遮罩以提高对比度 */
    backdrop-filter: none !important; /* 移除模糊效果 */
}

/* 确保弹出框在遮罩层之上 */
.driver-popover {
    position: relative !important;
    z-index: 1000001 !important; /* 确保在遮罩层之上 */
}

/* ===========================
   主弹出框 - 现代化卡片设计
   ========================== */

.driver-popover {
    /* 🎨 现代化卡片样式 */
    background: var(--guide-surface) !important;
    border: none !important;
    border-radius: var(--guide-radius-xl) !important;
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.15),
        0 8px 20px rgba(0, 0, 0, 0.10),
        0 0 0 1px var(--guide-border-light) !important;
    
    /* 📏 响应式尺寸 */
    max-width: min(420px, 90vw) !important;
    min-width: min(320px, 85vw) !important;
    width: auto !important;
    
    /* 🎭 动画效果 */
    animation: guidePopoverFadeIn var(--guide-animation-duration) var(--guide-animation-easing) !important;
    transform-origin: center !important;
    
    /* 📱 响应式定位 */
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    margin: 0 !important;
    z-index: 1000001 !important;
    
    /* 🔤 字体设置 */
    font-family: var(--guide-font-family) !important;
    
    /* 🖼️ 内容溢出处理 */
    overflow: hidden !important;
}

/* 移除所有之前的条件定位逻辑 */
.driver-popover[style*="top"][style*="left"] {
    display: none; /* 直接隐藏，避免冲突 */
}

.driver-popover.guide-step-welcome {
    /* 样式已在 .driver-popover 中强制应用 */
}

/* 遮罩层样式 - 确保全覆盖 */
.driver-overlay {
    background: rgba(0, 0, 0, 0.85) !important;
    backdrop-filter: blur(1px) !important;
    transition: all 0.3s ease-in-out !important;
    z-index: 999998 !important; /* 比弹出框低一层 */
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
}

/* 高亮区域样式 - 确保可见 */
.driver-highlighted-element {
    box-shadow: 
        0 0 0 4px #1976d2,
        0 0 0 8px rgba(25, 118, 210, 0.4),
        0 0 20px rgba(25, 118, 210, 0.6) !important;
    border-radius: var(--guide-radius) !important;
    transition: all 0.3s ease-in-out !important;
    background: rgba(255, 255, 255, 0.1) !important;
    position: relative !important;
    z-index: 999997 !important;
}

/* 确保弹出框在视口内 */
.driver-popover.driver-popover-top {
    transform: translateY(-10px) !important;
}

.driver-popover.driver-popover-bottom {
    transform: translateY(10px) !important;
}

.driver-popover.driver-popover-left {
    transform: translateX(-10px) !important;
}

.driver-popover.driver-popover-right {
    transform: translateX(10px) !important;
}

/* 弹出框内容区域增强 */
.driver-popover::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(45deg, #1976d2, #42a5f5);
    border-radius: 12px;
    z-index: -1;
    opacity: 0.8;
}

/* 弹出框箭头 */
.driver-popover.driver-popover-top .driver-popover-arrow,
.driver-popover.driver-popover-bottom .driver-popover-arrow,
.driver-popover.driver-popover-left .driver-popover-arrow,
.driver-popover.driver-popover-right .driver-popover-arrow {
    border-color: var(--guide-background) !important;
}

/* ===========================
   弹出框标题 - 现代化排版
   ========================== */

.driver-popover-title {
    /* 🎨 现代化标题样式 */
    color: var(--guide-text-primary) !important;
    font-size: var(--guide-font-size-title) !important;
    font-weight: 700 !important;
    line-height: 1.3 !important;
    
    /* 📏 间距 */
    margin: 0 !important;
    padding: var(--guide-spacing-lg) var(--guide-spacing-lg) var(--guide-spacing-sm) var(--guide-spacing-lg) !important;
    
    /* 🎭 视觉效果 */
    background: linear-gradient(135deg, var(--guide-primary), var(--guide-primary-light)) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

/* ===========================
   弹出框描述 - 现代化内容
   ========================== */

.driver-popover-description {
    /* 🎨 内容样式 */
    color: var(--guide-text-secondary) !important;
    font-size: var(--guide-font-size-body) !important;
    font-weight: 400 !important;
    line-height: 1.6 !important;
    
    /* 📏 间距 */
    margin: 0 !important;
    padding: 0 var(--guide-spacing-lg) var(--guide-spacing-base) var(--guide-spacing-lg) !important;
}

/* 强调文字样式 - 增强可见性 */
.driver-popover-description strong {
    color: #1976d2 !important; /* 深蓝色强调 */
    font-weight: 800 !important;
    background: rgba(25, 118, 210, 0.1) !important; /* 浅蓝背景 */
    padding: 2px 4px !important;
    border-radius: 3px !important;
    text-shadow: none !important;
}

/* ===========================
   弹出框底部 - 现代化布局
   ========================== */

.driver-popover-footer {
    /* 📏 布局 */
    padding: var(--guide-spacing-sm) var(--guide-spacing-lg) var(--guide-spacing-lg) var(--guide-spacing-lg) !important;
    border-top: 1px solid var(--guide-border-light) !important;
    background: var(--guide-surface) !important;
    border-radius: 0 0 var(--guide-radius-xl) var(--guide-radius-xl) !important;
    
    /* 🔄 Flexbox布局 */
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    gap: var(--guide-spacing-sm) !important;
    flex-wrap: wrap !important;
}

/* 进度指示器样式 - 增强可见性 */
.driver-popover-progress-text {
    color: #1976d2 !important;
    font-size: 14px !important;
    font-weight: 700 !important;
    margin-bottom: 16px !important;
    text-align: center !important;
    background: #e3f2fd !important; /* 浅蓝背景 */
    padding: 8px 16px !important;
    border-radius: 8px !important;
    border: 2px solid #1976d2 !important; /* 深蓝边框 */
    box-shadow: 0 2px 8px rgba(25, 118, 210, 0.2) !important;
}

/* ===========================
   按钮基础样式 - 现代化设计
   ========================== */

.driver-popover-footer button {
    /* 🎨 现代化按钮样式 */
    font-family: var(--guide-font-family) !important;
    font-size: var(--guide-font-size-small) !important;
    font-weight: 600 !important;
    line-height: 1.4 !important;
    
    /* 📏 尺寸和间距 - 防止换行 */
    padding: 10px 20px !important;
    min-width: 80px !important;
    max-width: 150px !important;
    height: 40px !important;
    
    /* 🎨 视觉样式 */
    border: 2px solid transparent !important;
    border-radius: var(--guide-radius-base) !important;
    cursor: pointer !important;
    
    /* 🎭 动画效果 */
    transition: all var(--guide-animation-duration) var(--guide-animation-easing) !important;
    
    /* 📝 文字处理 - 防止换行 */
    white-space: nowrap !important;
    text-overflow: ellipsis !important;
    overflow: hidden !important;
}

/* ===========================
   主要按钮样式 - 下一步/完成
   ========================== */

.driver-popover-next-btn,
.driver-popover-done-btn {
    /* 🎨 主要按钮样式 */
    background: linear-gradient(135deg, var(--guide-primary), var(--guide-primary-hover)) !important;
    color: var(--guide-text-inverse) !important;
    border-color: var(--guide-primary) !important;
    
    /* 🌟 阴影效果 */
    box-shadow: 
        0 4px 12px var(--guide-shadow),
        0 2px 4px rgba(25, 118, 210, 0.3) !important;
}

.driver-popover-next-btn:hover,
.driver-popover-done-btn:hover {
    background: linear-gradient(135deg, var(--guide-primary-hover), var(--guide-primary)) !important;
    transform: translateY(-2px) !important;
    box-shadow: 
        0 8px 20px var(--guide-shadow-hover),
        0 4px 8px rgba(25, 118, 210, 0.4) !important;
}

.driver-popover-next-btn:active,
.driver-popover-done-btn:active {
    transform: translateY(0) !important;
    box-shadow: 
        0 2px 8px var(--guide-shadow),
        0 1px 2px rgba(25, 118, 210, 0.3) !important;
}

.driver-popover-next-btn:focus,
.driver-popover-done-btn:focus {
    outline: none !important;
    box-shadow: 
        0 4px 12px var(--guide-shadow),
        0 0 0 3px var(--guide-shadow-focus) !important;
}

/* 次要按钮（上一步） - 增强对比度 */
.driver-popover-prev-btn {
    background: #ffffff !important; /* 纯白背景 */
    color: #1976d2 !important; /* 深蓝文字 */
    border: 2px solid #1976d2 !important; /* 深蓝边框 */
    font-weight: 700 !important;
}

.driver-popover-prev-btn:hover {
    background: #e3f2fd !important; /* 浅蓝背景 */
    border-color: #1565c0 !important;
    color: #1565c0 !important;
    transform: translateY(-1px) !important;
}

/* 关闭按钮 - 增强可见性 */
.driver-popover-close-btn {
    background: #ffffff !important;
    color: #757575 !important;
    border: 2px solid #bdbdbd !important;
    font-weight: 600 !important;
}

.driver-popover-close-btn:hover {
    color: #d32f2f !important; /* 红色悬停 */
    border-color: #d32f2f !important;
    background: #ffebee !important; /* 浅红背景 */
    transform: translateY(-1px) !important;
}

/* ===========================
   自定义步骤样式
   ========================== */

/* 欢迎步骤 */
.guide-step-welcome .driver-popover {
    max-width: 400px !important;
    text-align: center !important;
}

.guide-step-welcome .driver-popover-title {
    font-size: 24px !important;
    background: linear-gradient(135deg, var(--guide-primary-color), #9c27b0) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

/* 完成步骤 */
.guide-step-complete .driver-popover {
    max-width: 380px !important;
    text-align: center !important;
}

.guide-step-complete .driver-popover-title {
    font-size: 20px !important;
    color: var(--success-color, #4caf50) !important;
}

/* AI助手步骤特殊样式 */
.guide-step-ai .driver-popover-title {
    color: var(--guide-primary-color) !important;
}

/* ===========================
   响应式设计
   ========================== */

/* ===========================
   响应式设计规则
   ========================== */

/* 📱 平板设备适配 */
@media (max-width: 768px) {
    .driver-popover-title {
        font-size: 18px !important;
        padding: var(--guide-spacing-base) var(--guide-spacing-base) var(--guide-spacing-sm) var(--guide-spacing-base) !important;
    }
    
    .driver-popover-description {
        font-size: 15px !important;
        padding: 0 var(--guide-spacing-base) var(--guide-spacing-sm) var(--guide-spacing-base) !important;
    }
    
    .driver-popover-footer {
        padding: var(--guide-spacing-sm) var(--guide-spacing-base) var(--guide-spacing-base) var(--guide-spacing-base) !important;
        flex-direction: column !important;
        gap: var(--guide-spacing-xs) !important;
    }
    
    .driver-popover-footer button {
        font-size: 13px !important;
        padding: 8px 16px !important;
        min-width: 70px !important;
        max-width: 120px !important;
        height: 36px !important;
        flex: 1 !important;
        max-width: none !important;
    }
}

/* 📱 手机设备适配 */
@media (max-width: 480px) {
    .driver-popover-title {
        font-size: 16px !important;
        padding: var(--guide-spacing-sm) var(--guide-spacing-sm) var(--guide-spacing-xs) var(--guide-spacing-sm) !important;
    }
    
    .driver-popover-description {
        font-size: 14px !important;
        padding: 0 var(--guide-spacing-sm) var(--guide-spacing-xs) var(--guide-spacing-sm) !important;
    }
    
    .driver-popover-footer {
        padding: var(--guide-spacing-xs) var(--guide-spacing-sm) var(--guide-spacing-sm) var(--guide-spacing-sm) !important;
    }
    
    .driver-popover-footer button {
        font-size: 12px !important;
        padding: 6px 12px !important;
        min-width: 60px !important;
        height: 32px !important;
    }
}

/* ===========================
   超小屏幕优化 (320px及以下)
   ========================== */

@media (max-width: 360px) {
    .driver-popover {
        max-width: 95vw !important;
        min-width: 90vw !important;
        margin: 10px !important;
    }
    
    .driver-popover-title {
        font-size: 14px !important;
        padding: 8px 8px 4px 8px !important;
    }
    
    .driver-popover-description {
        font-size: 12px !important;
        padding: 0 8px 8px 8px !important;
    }
    
    .driver-popover-footer {
        padding: 4px 8px 8px 8px !important;
    }
    
    .driver-popover-footer button {
        font-size: 11px !important;
        padding: 4px 8px !important;
        height: 28px !important;
        min-width: 50px !important;
    }
}

/* ===========================
   可访问性增强
   ========================== */

/* 焦点样式 */
.driver-popover-footer button:focus {
    outline: 2px solid var(--guide-primary-color) !important;
    outline-offset: 2px !important;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .driver-overlay {
        background: rgba(0, 0, 0, 0.9) !important;
    }
    
    .driver-highlighted-element {
        box-shadow: 0 0 0 4px #ffffff, 
                    0 0 0 8px #000000 !important;
    }
    
    .driver-popover {
        border: 2px solid var(--guide-text-color) !important;
    }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
    .driver-overlay,
    .driver-highlighted-element,
    .driver-popover-footer button {
        transition: none !important;
    }
}

/* ===========================
   主题适配
   ========================== */

/* 深色主题适配 */
[data-theme="dark"] .user-guide-driver,
.theme-dark .user-guide-driver {
    --guide-background: var(--surface-color, #2d2d2d);
    --guide-text-color: var(--text-color, #ffffff);
    --guide-text-light: var(--text-light, #cccccc);
    --guide-border-color: var(--border-color, #404040);
    --guide-shadow: rgba(0, 0, 0, 0.3);
}

/* 外星人主题适配 */
[data-theme="alien"] .user-guide-driver,
.theme-alien .user-guide-driver {
    --guide-primary-color: var(--alien-primary, #00ff88);
    --guide-primary-hover: var(--alien-primary-hover, #00cc6a);
}

/* 暗色主题的特殊处理 */
[data-theme="dark"] .user-guide-driver,
[data-theme="dark"] .user-guide-driver-enhanced {
    --guide-background: #2d2d2d;
    --guide-text-color: #ffffff; /* 暗色主题使用白色文字 */
    --guide-text-light: #e0e0e0; /* 暗色主题使用浅灰色 */
    --guide-border-color: #404040;
}

/* 暗色主题下的弹出框标题 */
[data-theme="dark"] .driver-popover-title {
    color: #ffffff !important;
}

/* 暗色主题下的弹出框描述 */
[data-theme="dark"] .driver-popover-description {
    color: #e0e0e0 !important;
}

/* 暗色主题下的次要按钮 */
[data-theme="dark"] .driver-popover-prev-btn {
    background: #404040 !important;
    color: #ffffff !important;
    border-color: #606060 !important;
}

[data-theme="dark"] .driver-popover-prev-btn:hover {
    background: #505050 !important;
    border-color: var(--guide-primary-color) !important;
    color: var(--guide-primary-color) !important;
}

/* 外星人主题的特殊处理 */
[data-theme="alien"] .user-guide-driver,
[data-theme="alien"] .user-guide-driver-enhanced {
    --guide-primary-color: var(--alien-primary, #00ff88);
    --guide-background: #1a1a2e;
    --guide-text-color: #00ff88; /* 外星人主题使用绿色文字 */
    --guide-text-light: #88ffaa;
    --guide-border-color: #16213e;
}

/* 外星人主题下的弹出框标题 */
[data-theme="alien"] .driver-popover-title {
    color: #00ff88 !important;
}

/* 外星人主题下的弹出框描述 */
[data-theme="alien"] .driver-popover-description {
    color: #ccffdd !important;
}

/* 外星人主题下的高亮效果 */
[data-theme="alien"] .driver-highlighted-element {
    box-shadow: 0 0 0 4px var(--guide-primary-color),
                0 0 0 8px rgba(0, 255, 136, 0.3) !important;
}

/* 外星人主题下的次要按钮 */
[data-theme="alien"] .driver-popover-prev-btn {
    background: rgba(0, 255, 136, 0.1) !important;
    color: #00ff88 !important;
    border-color: rgba(0, 255, 136, 0.3) !important;
}

/* ===========================
   动画效果
   ========================== */

/* 弹出框出现动画 */
@keyframes guidePopoverFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.driver-popover {
    animation: guidePopoverFadeIn 0.3s ease-out !important;
}

/* 高亮元素脉冲效果 */
@keyframes guidePulse {
    0%, 100% {
        box-shadow: 0 0 0 4px var(--guide-primary-color), 
                    0 0 0 8px rgba(33, 150, 243, 0.3);
    }
    50% {
        box-shadow: 0 0 0 4px var(--guide-primary-color), 
                    0 0 0 12px rgba(33, 150, 243, 0.2);
    }
}

.driver-highlighted-element {
    animation: guidePulse 2s ease-in-out infinite !important;
}

/* ===========================
   工具类
   ========================== */

/* 隐藏引导元素 */
.user-guide-hidden {
    display: none !important;
}

/* 引导进行中的body样式 */
.user-guide-active {
    overflow: hidden !important;
}

/* 引导步骤计数器 */
.guide-step-counter {
    position: absolute;
    top: -12px;
    right: -12px;
    background: var(--guide-primary-color);
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* ===========================
   完成消息动画
   ========================== */

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* 引导完成消息样式 */
.guide-completion-message {
    font-family: var(--guide-font-family);
}

.guide-completion-message .message-content {
    display: flex;
    align-items: center;
    gap: 12px;
    color: var(--guide-text-color);
    font-size: 14px;
    line-height: 1.4;
}

.guide-completion-message .message-content i {
    font-size: 18px;
    flex-shrink: 0;
}

/* ===========================
   调试和开发工具
   ========================== */

/* 调试模式样式 */
.user-guide-debug {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px;
    border-radius: 6px;
    font-family: monospace;
    font-size: 12px;
    z-index: 10002;
    max-width: 300px;
}

.user-guide-debug h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #4CAF50;
}

.user-guide-debug ul {
    margin: 0;
    padding-left: 16px;
    list-style: none;
}

.user-guide-debug li {
    margin: 4px 0;
    padding-left: 16px;
    position: relative;
}

.user-guide-debug li:before {
    content: "•";
    position: absolute;
    left: 0;
    color: #2196F3;
}

/* 重置按钮样式 */
.guide-reset-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: var(--error-color, #f44336);
    color: white;
    border: none;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    font-size: 18px;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
    z-index: 10002;
    transition: all 0.2s ease-in-out;
}

.guide-reset-btn:hover {
    background: var(--error-dark, #d32f2f);
    transform: scale(1.1);
}

.guide-reset-btn:active {
    transform: scale(0.95);
}

/* ===========================
   交互式引导样式
   ========================== */

/* 菜单项高亮效果 */
.guide-menu-item-highlight {
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.1), rgba(33, 150, 243, 0.2)) !important;
    border: 2px solid var(--primary-color, #2196f3) !important;
    border-radius: 8px !important;
    box-shadow: 0 0 20px rgba(33, 150, 243, 0.4) !important;
    transform: scale(1.02) !important;
    transition: all 0.3s ease !important;
}

/* 高亮菜单项的悬停效果 */
.guide-menu-item-highlight:hover {
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.2), rgba(33, 150, 243, 0.3)) !important;
    box-shadow: 0 0 25px rgba(33, 150, 243, 0.6) !important;
    transform: scale(1.05) !important;
}

/* 交互提示框样式 */
.interactive-guide-prompt {
    animation: bounceIn 0.6s ease !important;
}

.interactive-guide-prompt .prompt-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.interactive-guide-prompt .prompt-icon {
    font-size: 32px;
    animation: pointDown 1.5s infinite ease-in-out;
}

.interactive-guide-prompt .prompt-text {
    font-size: 16px;
    font-weight: 600;
    color: #212121;
    margin: 0;
}

.interactive-guide-prompt .skip-interactive-btn {
    background: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;
    color: #666;
    cursor: pointer;
    transition: all 0.2s ease;
}

.interactive-guide-prompt .skip-interactive-btn:hover {
    background: #e0e0e0;
    color: #333;
}

/* 成功提示样式 */
.interaction-success-message {
    font-family: var(--guide-font-family);
}

.interaction-success-message .success-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.interaction-success-message .success-icon {
    font-size: 18px;
}

.interaction-success-message .success-text {
    font-size: 14px;
    font-weight: 600;
}

/* 动画定义 */
@keyframes pointDown {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(8px); }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.3);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.05);
    }
    70% {
        transform: translate(-50%, -50%) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

@keyframes slideInRight {
    0% {
        opacity: 0;
        transform: translateX(100%);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOutRight {
    0% {
        opacity: 1;
        transform: translateX(0);
    }
    100% {
        opacity: 0;
        transform: translateX(100%);
    }
}

/* 暗色主题下的交互样式 */
[data-theme="dark"] .interactive-guide-prompt {
    background: #2d2d2d !important;
    border-color: var(--primary-color, #2196f3) !important;
}

[data-theme="dark"] .interactive-guide-prompt .prompt-text {
    color: #ffffff !important;
}

[data-theme="dark"] .interactive-guide-prompt .skip-interactive-btn {
    background: #404040 !important;
    border-color: #606060 !important;
    color: #e0e0e0 !important;
}

[data-theme="dark"] .interactive-guide-prompt .skip-interactive-btn:hover {
    background: #505050 !important;
    color: #ffffff !important;
}

/* 外星人主题下的交互样式 */
[data-theme="alien"] .guide-menu-item-highlight {
    background: linear-gradient(135deg, rgba(0, 255, 136, 0.1), rgba(0, 255, 136, 0.2)) !important;
    border-color: var(--alien-primary, #00ff88) !important;
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.4) !important;
}

[data-theme="alien"] .guide-menu-item-highlight:hover {
    background: linear-gradient(135deg, rgba(0, 255, 136, 0.2), rgba(0, 255, 136, 0.3)) !important;
    box-shadow: 0 0 25px rgba(0, 255, 136, 0.6) !important;
}

[data-theme="alien"] .interactive-guide-prompt {
    background: #1a1a2e !important;
    border-color: var(--alien-primary, #00ff88) !important;
}

[data-theme="alien"] .interactive-guide-prompt .prompt-text {
    color: #00ff88 !important;
}

/* 暗色主题的极高对比度处理 */
[data-theme="dark"] .driver-popover {
    background: #1a1a1a !important; /* 深色背景 */
    border: 3px solid #42a5f5 !important; /* 亮蓝色边框 */
    box-shadow: 
        0 0 0 2px rgba(66, 165, 245, 0.4),
        0 16px 48px rgba(0, 0, 0, 0.8),
        0 8px 24px rgba(0, 0, 0, 0.6) !important;
}

[data-theme="dark"] .driver-popover::before {
    background: linear-gradient(45deg, #42a5f5, #90caf9);
}

[data-theme="dark"] .driver-popover-title {
    color: #ffffff !important; /* 纯白文字 */
    background: rgba(26, 26, 26, 0.95) !important;
    text-shadow: 
        0 0 4px rgba(0, 0, 0, 0.8),
        0 2px 4px rgba(0, 0, 0, 0.6) !important;
}

[data-theme="dark"] .driver-popover-description {
    color: #e3f2fd !important; /* 浅蓝白色 */
    background: rgba(26, 26, 26, 0.95) !important;
    text-shadow: 
        0 0 2px rgba(0, 0, 0, 0.8),
        0 1px 2px rgba(0, 0, 0, 0.6) !important;
}

[data-theme="dark"] .driver-popover-description strong {
    color: #90caf9 !important; /* 亮蓝色强调 */
    background: rgba(66, 165, 245, 0.2) !important;
}

[data-theme="dark"] .driver-popover-footer {
    background: rgba(26, 26, 26, 0.98) !important;
    border-top: 2px solid #1e88e5 !important;
}

[data-theme="dark"] .driver-popover-progress-text {
    color: #90caf9 !important;
    background: #0d47a1 !important;
    border-color: #42a5f5 !important;
}

[data-theme="dark"] .driver-popover-next-btn,
[data-theme="dark"] .driver-popover-done-btn {
    background: #1e88e5 !important;
    border-color: #1565c0 !important;
}

[data-theme="dark"] .driver-popover-prev-btn {
    background: #1a1a1a !important;
    color: #90caf9 !important;
    border-color: #42a5f5 !important;
}

[data-theme="dark"] .driver-popover-prev-btn:hover {
    background: #0d47a1 !important;
    color: #e3f2fd !important;
}

/* 外星人主题的极高对比度处理 */
[data-theme="alien"] .driver-popover {
    background: #0a0a1a !important; /* 深紫背景 */
    border: 3px solid #00ff88 !important; /* 亮绿边框 */
    box-shadow: 
        0 0 0 2px rgba(0, 255, 136, 0.4),
        0 16px 48px rgba(0, 255, 136, 0.3),
        0 8px 24px rgba(0, 0, 0, 0.6) !important;
}

[data-theme="alien"] .driver-popover::before {
    background: linear-gradient(45deg, #00ff88, #00cc6a);
}

[data-theme="alien"] .driver-popover-title {
    color: #00ff88 !important;
    background: rgba(10, 10, 26, 0.95) !important;
    text-shadow: 
        0 0 8px rgba(0, 255, 136, 0.6),
        0 2px 4px rgba(0, 255, 136, 0.4) !important;
}

[data-theme="alien"] .driver-popover-description {
    color: #ccffdd !important;
    background: rgba(10, 10, 26, 0.95) !important;
    text-shadow: 
        0 0 4px rgba(0, 255, 136, 0.4),
        0 1px 2px rgba(0, 255, 136, 0.2) !important;
}

[data-theme="alien"] .driver-popover-description strong {
    color: #00ff88 !important;
    background: rgba(0, 255, 136, 0.2) !important;
    box-shadow: 0 0 4px rgba(0, 255, 136, 0.3) !important;
}

[data-theme="alien"] .driver-popover-footer {
    background: rgba(10, 10, 26, 0.98) !important;
    border-top: 2px solid #00cc6a !important;
}

[data-theme="alien"] .driver-popover-progress-text {
    color: #00ff88 !important;
    background: #001a0f !important;
    border-color: #00ff88 !important;
    box-shadow: 0 0 8px rgba(0, 255, 136, 0.3) !important;
}

[data-theme="alien"] .driver-popover-next-btn,
[data-theme="alien"] .driver-popover-done-btn {
    background: #00cc6a !important;
    border-color: #00aa55 !important;
    box-shadow: 
        0 0 0 2px rgba(0, 255, 136, 0.3),
        0 4px 12px rgba(0, 255, 136, 0.4) !important;
}

[data-theme="alien"] .driver-popover-prev-btn {
    background: #0a0a1a !important;
    color: #00ff88 !important;
    border-color: #00ff88 !important;
}

[data-theme="alien"] .driver-popover-prev-btn:hover {
    background: #001a0f !important;
    color: #ccffdd !important;
}
